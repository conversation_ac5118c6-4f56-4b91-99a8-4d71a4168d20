---
description: 
globs: 
alwaysApply: true
---
# Inbox Zero AI - Master Rule Index

This document serves as the central index for all active Cursor rules within this repository, organized by category. These rules define processes, structures, and guidelines for development.

## Core & General

Fundamental project structure, setup, and general development guidelines.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @cursor-rules.mdc                  | How to add or edit Cursor rules in our project              |
| @project-structure.mdc             | Project structure and file organization guidelines          |
| @installing-packages.mdc           | How to install packages                                     |
| @environment-variables.mdc         | Add environment variable                                    |
| @logging.mdc                       | How to do backend logging                                   |
| @utilities.mdc                     | Util functions                                              |
| @task-list.mdc                     | Guidelines for creating and managing task lists             |

## Frontend & UI

Guidelines for building user interfaces and handling frontend logic.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @page-structure.mdc                | Page structure guidelines                                   |
| @ui-components.mdc                 | UI component and styling guidelines (Shadcn, Tailwind)      |
| @form-handling.mdc                 | Form handling using React Hook Form and Zod                 |
| @data-fetching.mdc                 | Fetching data from the API using SWR                        |
| @hooks.mdc                         | Guidelines for creating custom React hooks                  |

## Backend & API

Guidelines for implementing backend logic, APIs, and data persistence.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @get-api-route.mdc                 | Guidelines for implementing GET API routes in Next.js       |
| @server-actions.mdc                | Guidelines for implementing Next.js server actions          |
| @prisma.mdc                        | How to use Prisma                                           |
| @gmail-api.mdc                     | Guidelines for working with Gmail API                       |

## AI / LLM

Guidelines specific to implementing and testing AI and Language Model features.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @llm.mdc                           | Guidelines for implementing LLM functionality               |
| @llm-test.mdc                      | Guidelines for writing tests for LLM-related functionality  |

## Testing

General guidelines for application testing.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @testing.mdc                       | Guidelines for testing the application with Vitest          |

## Features

Rules specific to major application features.

| Rule File                          | Description                                                 |
| :--------------------------------- | :---------------------------------------------------------- |
| @features/cleaner.mdc              | Explains the Inbox Cleaner feature                          |
| @features/knowledge.mdc            | Explains the Knowledge Base feature                         |
| @features/reply-tracker.mdc        | Explains the Reply Tracker (Reply Zero) feature             |