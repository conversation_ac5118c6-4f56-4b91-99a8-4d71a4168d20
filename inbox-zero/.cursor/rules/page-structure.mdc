---
description: Page structure
globs: 
alwaysApply: false
---
# Page Structure

- Create new pages at: `apps/web/app/(app)/PAGE_NAME/page.tsx`
- Components for the page are either put in `page.tsx`, or in the `apps/web/app/(app)/PAGE_NAME` folder
- Pages are Server components so you can load data into them directly
- If we're in a deeply nested component we will use `swr` to fetch via API
- If you need to use `onClick` in a component, that component is a client component and file must start with `use client` 