{"name": "@inbox-zero/mcp-server", "version": "0.0.1", "private": true, "type": "module", "bin": {"inbox-zero-ai": "./build/index.js"}, "scripts": {"start": "node build/index.js", "build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\""}, "files": ["build"], "dependencies": {"@modelcontextprotocol/sdk": "1.12.1", "zod": "3.25.46"}, "devDependencies": {"@types/node": "22.15.29", "typescript": "5.8.3"}}