{"name": "inbox-zero-unsubscriber", "version": "1.0.0", "private": true, "type": "module", "main": "index.js", "scripts": {"start": "tsx --tsconfig tsconfig.json src/server.ts", "dev": "tsx watch --tsconfig tsconfig.json src/server.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@types/dotenv": "8.2.3", "@types/node": "22.15.29", "playwright": "1.52.0", "tsx": "4.19.4", "typescript": "5.8.3"}, "dependencies": {"@ai-sdk/amazon-bedrock": "2.2.9", "@ai-sdk/anthropic": "1.2.12", "@ai-sdk/google": "1.2.18", "@ai-sdk/openai": "1.3.22", "@fastify/cors": "11.0.1", "@t3-oss/env-core": "0.13.6", "ai": "4.3.16", "dotenv": "16.5.0", "fastify": "5.3.3", "zod": "3.25.46"}}