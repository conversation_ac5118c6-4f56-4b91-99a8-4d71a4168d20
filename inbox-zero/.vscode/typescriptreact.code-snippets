{
  // based on: https://github.com/theodorusclarence/ts-nextjs-tailwind-starter/blob/main/.vscode/typescriptreact.code-snippets
  //#region  //*=========== React ===========
  "React.useState": {
    "prefix": "us",
    "body": [
      "const [${1}, set${1/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}] = React.useState<$3>(${2:initial${1/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}})$0"
    ]
  },
  "React.useEffect": {
    "prefix": "uf",
    "body": ["React.useEffect(() => {", "  $0", "}, []);"]
  },
  "React Functional Component": {
    "prefix": "rc",
    "body": [
      "export function ${1:${TM_FILENAME_BASE}}(props: {}) {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  )",
      "}"
    ]
  },
  //#endregion  //*======== React ===========

  //#region  //*=========== Nextjs ===========
  "Next API Route": {
    "prefix": "napi",
    "body": [
      "import { z } from \"zod\";",
      "import { NextResponse } from \"next/server\";",
      "import { auth } from \"@/app/api/auth/[...nextauth]/auth\";",
      "import prisma from \"@/utils/prisma\";",
      "import { withAuth } from \"@/utils/middleware\";",
      "",
      "export type ${1:ApiName}Response = Awaited<",
      "  ReturnType<typeof get${1:ApiName}>",
      ">;",
      "",
      "async function get${1:ApiName}(options: { userId: string }) {",
      "  const result = await prisma.${2:table}.findMany({",
      "    where: {",
      "      userId: options.userId,",
      "    },",
      "  });",
      "  return { result };",
      "}",
      "",
      "export const GET = withAuth(async (request) => {",
      "  const result = await get${1:ApiName}({ userId: session.user.id });",
      "",
      "  return NextResponse.json(result);",
      "});",
      "",
      "const ${1:ApiName}Body = z.object({ message: z.string() });",
      "export type ${1/(.*)/${1:/downcase}/}Body = z.infer<typeof ${1:ApiName}Body>;",
      "export type update${1:ApiName}Response = Awaited<ReturnType<typeof ${1:ApiName}>>;",
      "",
      "export const POST = withAuth(async (request) => {",
      "  const json = await request.json();",
      "  const body = ${1/(.*)/${1:/downcase}/}Body.parse(json);",
      "",
      "  const result = await prisma.${2:table}.update({",
      "    where: {",
      "      id: params.id,",
      "      userId: session.user.id,",
      "    },",
      "    data: body",
      "  })",
      "",
      "  return NextResponse.json(result);",
      "});",
      ""
    ],
    "description": "Next API Route"
  },
  "Next API GET Route": {
    "prefix": "napig",
    "body": [
      "import { z } from \"zod\";",
      "import { NextResponse } from \"next/server\";",
      "import { auth } from \"@/app/api/auth/[...nextauth]/auth\";",
      "import prisma from \"@/utils/prisma\";",
      "import { withAuth } from \"@/utils/middleware\";",
      "",
      "export type ${1:ApiName}Response = Awaited<",
      "  ReturnType<typeof get${1:ApiName}>",
      ">;",
      "",
      "async function get${1:ApiName}(options: { email: string }) {",
      "  const result = await prisma.${2:table}.findMany({",
      "    where: {",
      "      email: options.email,",
      "    },",
      "  });",
      "  return { result };",
      "};",
      "",
      "export const GET = withAuth(async () => {",
      "  const result = await get${1:ApiName}({ email: session.user.email });",
      "",
      "  return NextResponse.json(result);",
      "});",
      ""
    ],
    "description": "Next API GET Route"
  },
  "Next API POST Route": {
    "prefix": "napip",
    "body": [
      "import { z } from \"zod\";",
      "import { NextResponse } from \"next/server\";",
      "import { auth } from \"@/app/api/auth/[...nextauth]/auth\";",
      "import prisma from \"@/utils/prisma\";",
      "import { withAuth } from \"@/utils/middleware\";",
      "",
      "const ${1:ApiName}Body = z.object({ id: z.string(), message: z.string() });",
      "export type ${1/(.*)/${1:/downcase}/}Body = z.infer<typeof ${1:ApiName}Body>;",
      "export type update${1:ApiName}Response = Awaited<ReturnType<typeof update${1:ApiName}>>;",
      "",
      "async function update${1:ApiName}(body: ${1/(.*)/${1:/downcase}/}Body, options: { email: string }) {",
      "  const { email } = options;",
      "  const result = await prisma.${2:table}.update({",
      "    where: {",
      "      id: body.id,",
      "      email,",
      "    },",
      "    data: body",
      "  })",
      "",
      "  return { result };",
      "};",
      "",
      "export const POST = withAuth(async (request: Request) => {",
      "  const json = await request.json();",
      "  const body = ${1/(.*)/${1:/downcase}/}Body.parse(json);",
      "",
      "  const result = await update${1:ApiName}(body, { email: session.user.email });",
      "",
      "  return NextResponse.json(result);",
      "});",
      ""
    ],
    "description": "Next API POST Route"
  },
  //#endregion  //*======== Nextjs ===========

  //#region  //*=========== Snippet Wrap ===========
  "Wrap with Fragment": {
    "prefix": "ff",
    "body": ["<>", "\t${TM_SELECTED_TEXT}", "</>"]
  },
  "Wrap with clsx": {
    "prefix": "cx",
    "body": ["{clsx(${TM_SELECTED_TEXT}$0)}"]
  },
  "Wrap with memo": {
    "prefix": "mem",
    "body": ["const value = useMemo(() => (${TM_SELECTED_TEXT}), [])"]
  },
  //#endregion  //*======== Snippet Wrap ===========

  //#region  //*=========== Custom ===========
  "Form + API": {
    "prefix": "formapi",
    "body": [
      "// api/example/route.ts",
      "",
      "import { NextResponse } from \"next/server\";",
      "import { auth } from \"@/app/api/auth/[...nextauth]/auth\";",
      "import prisma from \"@/utils/prisma\";",
      "import { withAuth } from \"@/utils/middleware\";",
      "import {",
      "  SaveSettingsBody,",
      "  saveSettingsBody,",
      "} from \"@/app/api/user/settings/validation\";",
      "import { SafeError } from \"@/utils/error\";",
      "",
      "export type SaveSettingsResponse = Awaited<ReturnType<typeof saveAISettings>>;",
      "",
      "async function saveAISettings(options: SaveSettingsBody) {",
      "  return await prisma.user.update({",
      "    where: { email: session.user.email },",
      "    data: {",
      "      aiModel: options.aiModel,",
      "      aiApiKey: options.aiApiKey,",
      "    },",
      "  });",
      "}",
      "",
      "export const POST = withAuth(async (request: Request) => {",
      "  const json = await request.json();",
      "  const body = saveSettingsBody.parse(json);",
      "",
      "  const result = await saveAISettings(body);",
      "",
      "  return NextResponse.json(result);",
      "});",
      "",
      "// api/example/validation.ts - so we can share zod validation and types with client",
      "",
      "import { z } from \"zod\";",
      "",
      "export const saveSettingsBody = z.object({",
      "  aiModel: z.string().optional(),",
      "  aiApiKey: z.string().optional(),",
      "});",
      "export type SaveSettingsBody = z.infer<typeof saveSettingsBody>;",
      "",
      "// client file",
      "",
      "\"use client\";",
      "",
      "import { useCallback } from \"react\";",
      "import { SubmitHandler, useForm } from \"react-hook-form\";",
      "import useSWR from \"swr\";",
      "import { Button } from \"@/components/Button\";",
      "import { FormSection, FormSectionLeft } from \"@/components/Form\";",
      "import { toastError, toastSuccess } from \"@/components/Toast\";",
      "import { Input } from \"@/components/Input\";",
      "import { isError } from \"@/utils/error\";",
      "import { zodResolver } from \"@hookform/resolvers/zod\";",
      "import { LoadingContent } from \"@/components/LoadingContent\";",
      "import { UserResponse } from \"@/app/api/user/me/route\";",
      "import {",
      "  saveSettingsBody,",
      "  SaveSettingsBody,",
      "} from \"@/app/api/user/settings/validation\";",
      "import { SaveSettingsResponse } from \"@/app/api/user/settings/route\";",
      "import { Select } from \"@/components/Select\";",
      "",
      "export function ModelSection() {",
      "  const { data, isLoading, error } = useSWR<UserResponse>(\"/api/user/me\");",
      "",
      "  return (",
      "    <FormSection>",
      "      <FormSectionLeft",
      "        title=\"AI Model\"",
      "        description=\"Use your own API key and choose your AI model.\"",
      "      />",
      "",
      "      <LoadingContent loading={isLoading} error={error}>",
      "        {data && (",
      "          <ModelSectionForm",
      "            aiModel={data.aiModel}",
      "            aiApiKey={data.aiApiKey}",
      "          />",
      "        )}",
      "      </LoadingContent>",
      "    </FormSection>",
      "  );",
      "}",
      "",
      "function ModelSectionForm(props: {",
      "  aiModel: string | null;",
      "  aiApiKey: string | null;",
      "}) {",
      "  const {",
      "    register,",
      "    handleSubmit,",
      "    formState: { errors, isSubmitting },",
      "  } = useForm<SaveSettingsBody>({",
      "    resolver: zodResolver(saveSettingsBody),",
      "    defaultValues: {",
      "      aiModel: props.aiModel ?? undefined,",
      "      aiApiKey: props.aiApiKey ?? undefined,",
      "    },",
      "  });",
      "",
      "  const onSubmit: SubmitHandler<SaveSettingsBody> = useCallback(",
      "    async (data) => {",
      "      const res = await myAction(emailAccountId, data);",
      "",
      "      if (res?.serverError) {",
      "        toastError({",
      "          description: \"There was an error updating the settings.\",",
      "        });",
      "      } else {",
      "        toastSuccess({ description: \"Settings updated!\" });",
      "      }",
      "    },",
      "    []",
      "  );",
      "",
      "  return (",
      "    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">",
      "      <Select",
      "        name=\"aiModel\"",
      "        label=\"Model\"",
      "        options={[",
      "          {",
      "            label: \"GPT-4\",",
      "            value: \"gpt-4\",",
      "          },",
      "        ]}",
      "        registerProps={register(\"aiModel\")}",
      "        error={errors.aiModel}",
      "      />",
      "",
      "      <Input",
      "        type=\"password\"",
      "        name=\"aiApiKey\"",
      "        label=\"API Key\"",
      "        registerProps={register(\"aiApiKey\")}",
      "        error={errors.aiApiKey}",
      "      />",
      "      <Button type=\"submit\" loading={isSubmitting}>",
      "        Save",
      "      </Button>",
      "    </form>",
      "  );",
      "}",
      ""
    ],
    "description": "Form + API"
  },

  "React Hook Form": {
    "prefix": "rhf",
    "body": [
      "import { useCallback } from 'react';",
      "import { SubmitHandler, useForm } from 'react-hook-form';",
      "import { Button } from '@/components/Button';",
      "import { Input } from '@/components/Input';",
      "import { toastSuccess, toastError } from '@/components/Toast';",
      "import { isErrorMessage } from '@/utils/error';",
      "",
      "type Inputs = { address: string };",
      "",
      "const ${1:${TM_FILENAME_BASE/(^[a-zA-Z])(.*)/${1:/upcase}${2}/}}Form = () => {",
      "  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<Inputs>();",
      "",
      "  const onSubmit: SubmitHandler<Inputs> = useCallback(",
      "    async data => {",
      "      const res = await updateProfile(data)",
      "      if (isErrorMessage(res)) toastError({ description: `` });",
      "      else toastSuccess({ description: `` });",
      "    },",
      "    []",
      "  );",
      "",
      "  return (",
      "    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">",
      "      <Input",
      "        type=\"text\"",
      "        name=\"address\"",
      "        label=\"Address\"",
      "        registerProps={register('address', { required: true })}",
      "        error={errors.address}",
      "      />",
      "      <Button type=\"submit\" loading={isSubmitting}>",
      "        Add",
      "      </Button>",
      "    </form>",
      "  );",
      "}"
    ]
  },
  "SWR": {
    "prefix": "swr",
    "body": [
      "\"use client\";",
      "",
      "import useSWR from \"swr\";",
      "import { LoadingContent } from \"@/components/LoadingContent\";",
      "",
      "export default function Page() {",
      "  const { data, isLoading, error } = useSWR<Response, { error: string }>(`/api/user/stats`);",
      "",
      "  return (",
      "    <LoadingContent loading={isLoading} error={error}>",
      "      {data && (",
      "        <div />",
      "      )}",
      "    </LoadingContent>",
      "  );",
      "}",
      ""
    ],
    "description": "SWR"
  },
  //#endregion  //*======== Custom ===========

  "Logger": {
    "prefix": "lg",
    "body": [
      "console.log({ ${1:${CLIPBOARD}} }, '${TM_FILENAME} line ${TM_LINE_NUMBER}')"
    ]
  },
  "Simple Logger": {
    "prefix": "cl",
    "body": ["console.log('$1')"]
  },
  "Error Logger": {
    "prefix": "ce",
    "body": ["console.error('$1')"]
  },
  "Use Client": {
    "prefix": "uc",
    "body": ["'use client';\n"]
  }
}
