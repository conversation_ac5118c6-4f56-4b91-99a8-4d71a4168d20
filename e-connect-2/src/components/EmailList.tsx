import * as React from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/Card";
import { LoadingContent } from "@/components/ui/Loading";
import { Button } from "@/components/ui/Button";
import { formatShortDate } from "@/utils/date";
import { extractNameFromEmail } from "@/utils/email";
import { QUERY_KEYS } from "@/utils/config";
import type { ParsedMessage } from "@/utils/types";

// Mock API function - this would be replaced with actual API calls
async function fetchEmails(): Promise<{ messages: ParsedMessage[] }> {
  const response = await fetch('/api/google/messages');
  if (!response.ok) {
    throw new Error('Failed to fetch emails');
  }
  return response.json();
}

interface EmailItemProps {
  email: ParsedMessage;
  onSelect: (email: ParsedMessage) => void;
  isSelected: boolean;
}

function EmailItem({ email, onSelect, isSelected }: EmailItemProps) {
  const senderName = extractNameFromEmail(email.from.email);
  const date = new Date(email.date);

  return (
    <div
      className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${
        isSelected ? 'bg-blue-50 border-blue-200' : ''
      } ${!email.read ? 'font-semibold' : ''}`}
      onClick={() => onSelect(email)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <p className="text-sm font-medium text-gray-900 truncate">
              {senderName || email.from.email}
            </p>
            <p className="text-xs text-gray-500 ml-2">
              {formatShortDate(date)}
            </p>
          </div>
          <p className="text-sm text-gray-900 mb-1 truncate">
            {email.headers.subject}
          </p>
          <p className="text-sm text-gray-500 truncate">
            {email.snippet}
          </p>
        </div>
        {!email.read && (
          <div className="ml-2 flex-shrink-0">
            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
          </div>
        )}
      </div>
    </div>
  );
}

interface EmailListProps {
  onEmailSelect?: (email: ParsedMessage) => void;
  selectedEmailId?: string;
}

export function EmailList({ onEmailSelect, selectedEmailId }: EmailListProps) {
  const { data, isLoading, error } = useQuery({
    queryKey: [QUERY_KEYS.EMAILS],
    queryFn: fetchEmails,
  });

  const handleEmailSelect = (email: ParsedMessage) => {
    onEmailSelect?.(email);
  };

  const handleRefresh = () => {
    // This would trigger a refetch
    window.location.reload();
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Inbox</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <LoadingContent loading={isLoading} error={error}>
          {data?.messages && data.messages.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {data.messages.map((email) => (
                <EmailItem
                  key={email.id}
                  email={email}
                  onSelect={handleEmailSelect}
                  isSelected={email.id === selectedEmailId}
                />
              ))}
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <p>No emails found</p>
            </div>
          )}
        </LoadingContent>
      </CardContent>
    </Card>
  );
}
