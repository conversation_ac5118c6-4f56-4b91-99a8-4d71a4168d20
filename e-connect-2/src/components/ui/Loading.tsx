import * as React from "react";
import { cn } from "@/utils";

// Loading spinner SVG
const LoadingSpinner = ({ className }: { className?: string }) => (
  <svg
    className={cn("animate-spin", className)}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
);

export function Loading({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center p-8", className)}>
      <LoadingSpinner className="h-8 w-8 text-gray-600" />
    </div>
  );
}

export function LoadingMiniSpinner({ className }: { className?: string }) {
  return <LoadingSpinner className={cn("h-4 w-4", className)} />;
}

export function ButtonLoader({ className }: { className?: string }) {
  return <LoadingSpinner className={cn("mr-2 h-4 w-4", className)} />;
}

// Loading content wrapper
interface LoadingContentProps {
  loading: boolean;
  loadingComponent?: React.ReactNode;
  error?: { message?: string } | string;
  errorComponent?: React.ReactNode;
  children: React.ReactNode;
}

export function LoadingContent({
  loading,
  loadingComponent,
  error,
  errorComponent,
  children,
}: LoadingContentProps) {
  if (error) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }
    
    const errorMessage = typeof error === 'string' ? error : error.message || 'An error occurred';
    
    return (
      <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{errorMessage}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return <>{loadingComponent || <Loading />}</>;
  }

  return <>{children}</>;
}

// Skeleton loading components
export function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-gray-200", className)}
      {...props}
    />
  );
}

export function SkeletonText({ lines = 3 }: { lines?: number }) {
  return (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            "h-4",
            i === lines - 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  );
}

export function SkeletonCard() {
  return (
    <div className="p-4 border border-gray-200 rounded-lg">
      <div className="space-y-3">
        <Skeleton className="h-4 w-1/4" />
        <SkeletonText lines={2} />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    </div>
  );
}
