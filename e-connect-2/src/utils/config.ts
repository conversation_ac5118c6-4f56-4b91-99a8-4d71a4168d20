// Application configuration and constants

export const APP_NAME = 'E-Connect 2';
export const APP_DESCRIPTION = 'Email management powered by AI';

// API endpoints
export const API_BASE_URL = '/api';

// Email constants
export const EMAIL_LABELS = {
  INBOX: 'INBOX',
  SENT: 'SENT',
  DRAFT: 'DRAFT',
  SPAM: 'SPAM',
  TRASH: 'TRASH',
  IMPORTANT: 'IMPORTANT',
  STARRED: 'STARRED',
} as const;

export const EMAIL_CATEGORIES = {
  NEWSLETTER: 'newsletter',
  RECEIPT: 'receipt',
  MARKETING: 'marketing',
  PERSONAL: 'personal',
  WORK: 'work',
  SOCIAL: 'social',
  UPDATES: 'updates',
  FORUMS: 'forums',
  PROMOTIONS: 'promotions',
} as const;

// Pagination
export const DEFAULT_PAGE_SIZE = 50;
export const MAX_PAGE_SIZE = 100;

// File upload
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'text/plain',
  'text/csv',
  'application/json',
];

// AI models
export const AI_MODELS = {
  GPT_4: 'gpt-4',
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
  CLAUDE_3_SONNET: 'claude-3-sonnet',
  CLAUDE_3_HAIKU: 'claude-3-haiku',
} as const;

// Theme
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'e-connect-theme',
  SIDEBAR_COLLAPSED: 'e-connect-sidebar-collapsed',
  EMAIL_VIEW_MODE: 'e-connect-email-view-mode',
  LAST_SELECTED_ACCOUNT: 'e-connect-last-account',
} as const;

// Query keys for React Query
export const QUERY_KEYS = {
  USER: 'user',
  EMAILS: 'emails',
  THREADS: 'threads',
  RULES: 'rules',
  CHATS: 'chats',
  SETTINGS: 'settings',
  LABELS: 'labels',
  CATEGORIES: 'categories',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`,
  INVALID_FILE_TYPE: 'Invalid file type. Please select a supported file.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  EMAIL_SENT: 'Email sent successfully',
  EMAIL_ARCHIVED: 'Email archived',
  EMAIL_DELETED: 'Email deleted',
  RULE_CREATED: 'Rule created successfully',
  RULE_UPDATED: 'Rule updated successfully',
  RULE_DELETED: 'Rule deleted successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
} as const;

// Feature flags
export const FEATURES = {
  AI_CATEGORIZATION: true,
  BULK_ACTIONS: true,
  EMAIL_TEMPLATES: true,
  ADVANCED_SEARCH: true,
  DARK_MODE: true,
  KEYBOARD_SHORTCUTS: true,
} as const;

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  COMPOSE: 'c',
  ARCHIVE: 'e',
  DELETE: 'Delete',
  REPLY: 'r',
  REPLY_ALL: 'a',
  FORWARD: 'f',
  MARK_READ: 'i',
  MARK_UNREAD: 'u',
  STAR: 's',
  SEARCH: '/',
  REFRESH: 'g',
  SELECT_ALL: 'Ctrl+a',
  ESCAPE: 'Escape',
} as const;

// Email view modes
export const EMAIL_VIEW_MODES = {
  LIST: 'list',
  CONVERSATION: 'conversation',
  SPLIT: 'split',
} as const;

// Animation durations (in ms)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Z-index values
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;
