// Core type definitions for E-Connect 2

// https://stackoverflow.com/a/53276873/2602771
export type PartialRecord<K extends keyof any, T> = Partial<Record<K, T>>;

// type guard for filters that removed undefined and null values
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null;
}

// Email related types
export interface EmailAddress {
  name?: string;
  email: string;
}

export interface ParsedMessage {
  id: string;
  threadId: string;
  labelIds?: string[];
  snippet: string;
  historyId: string;
  attachments?: Attachment[];
  headers: ParsedMessageHeaders;
  textPlain?: string;
  textHtml?: string;
  date: string;
  read: boolean;
  from: EmailAddress;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
}

export interface Attachment {
  filename: string;
  mimeType: string;
  size: number;
  attachmentId: string;
  headers: {
    "content-type": string;
    "content-description": string;
    "content-transfer-encoding": string;
    "content-id": string;
  };
}

export interface ParsedMessageHeaders {
  subject: string;
  from: string;
  to: string;
  cc?: string;
  bcc?: string;
  date: string;
  "message-id"?: string;
  "reply-to"?: string;
  "in-reply-to"?: string;
  references?: string;
  "list-unsubscribe"?: string;
}

export type EmailForLLM = {
  id: string;
  from: string;
  to: string;
  replyTo?: string;
  cc?: string;
  subject: string;
  content: string;
  date?: Date;
};

// Rule related types
export interface Rule {
  id: string;
  name: string;
  instructions: string;
  enabled: boolean;
  createdAt: string;
  updatedAt?: string;
  actions?: RuleAction[];
}

export interface RuleAction {
  id: string;
  type: 'archive' | 'label' | 'forward' | 'reply' | 'delete';
  value?: string;
}

// Chat related types
export interface Chat {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages?: ChatMessage[];
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt: string;
}

// User related types
export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  settings?: UserSettings;
}

export interface UserSettings {
  aiModel?: string;
  autoArchive?: boolean;
  emailSignature?: string;
  theme?: 'light' | 'dark' | 'system';
}

// API response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  nextPageToken?: string;
  totalCount?: number;
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

// Thread related types
export interface Thread {
  id: string;
  messages: ParsedMessage[];
  snippet: string;
  historyId: string;
}
