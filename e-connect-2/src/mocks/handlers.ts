import { http, HttpResponse } from 'msw'

// Mock data
const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: '<PERSON>',
  image: 'https://via.placeholder.com/40',
}

const mockEmails = [
  {
    id: 'email-1',
    threadId: 'thread-1',
    subject: 'Welcome to E-Connect',
    from: { name: 'E-Connect Team', email: '<EMAIL>' },
    to: [{ name: '<PERSON>', email: '<EMAIL>' }],
    date: new Date().toISOString(),
    snippet: 'Welcome to E-Connect! We are excited to have you on board.',
    read: false,
    labels: ['INBOX'],
  },
  {
    id: 'email-2',
    threadId: 'thread-2',
    subject: 'Your daily digest',
    from: { name: 'Newsletter', email: '<EMAIL>' },
    to: [{ name: '<PERSON>', email: '<EMAIL>' }],
    date: new Date(Date.now() - 86400000).toISOString(),
    snippet: 'Here is your daily digest of important updates.',
    read: true,
    labels: ['INBOX'],
  },
]

const mockChats = [
  {
    id: 'chat-1',
    title: 'Email Management Help',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

const mockRules = [
  {
    id: 'rule-1',
    name: 'Archive Newsletters',
    instructions: 'Archive all newsletter emails',
    enabled: true,
    createdAt: new Date().toISOString(),
  },
]

export const handlers = [
  // Authentication endpoints
  http.get('/api/auth/session', () => {
    return HttpResponse.json({
      user: mockUser,
      expires: new Date(Date.now() + 86400000).toISOString(),
    })
  }),

  // User endpoints
  http.get('/api/user/me', () => {
    return HttpResponse.json(mockUser)
  }),

  // Gmail/Google API endpoints
  http.get('/api/google/messages', ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q')
    const pageToken = url.searchParams.get('pageToken')
    
    return HttpResponse.json({
      messages: mockEmails,
      nextPageToken: pageToken ? null : 'next-page-token',
    })
  }),

  http.get('/api/google/threads', () => {
    return HttpResponse.json({
      threads: mockEmails.map(email => ({
        id: email.threadId,
        messages: [email],
      })),
    })
  }),

  // Chat endpoints
  http.get('/api/chats', () => {
    return HttpResponse.json({ chats: mockChats })
  }),

  http.get('/api/chats/:chatId', ({ params }) => {
    const chat = mockChats.find(c => c.id === params.chatId)
    if (!chat) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(chat)
  }),

  // Rules endpoints
  http.get('/api/user/rules', () => {
    return HttpResponse.json({ rules: mockRules })
  }),

  http.post('/api/user/rules', async ({ request }) => {
    const body = await request.json()
    const newRule = {
      id: `rule-${Date.now()}`,
      ...body,
      createdAt: new Date().toISOString(),
    }
    mockRules.push(newRule)
    return HttpResponse.json(newRule, { status: 201 })
  }),

  // AI endpoints
  http.post('/api/ai/categorize', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      category: 'newsletter',
      confidence: 0.95,
    })
  }),

  http.post('/api/ai/summarize', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      summary: 'This is a mock summary of the email content.',
    })
  }),

  // Settings endpoints
  http.get('/api/user/settings', () => {
    return HttpResponse.json({
      aiModel: 'gpt-4',
      autoArchive: true,
      emailSignature: 'Best regards,\nJohn Doe',
    })
  }),

  http.patch('/api/user/settings', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json(body)
  }),
]
