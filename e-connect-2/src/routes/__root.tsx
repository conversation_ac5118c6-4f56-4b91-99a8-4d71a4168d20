import { createRoot<PERSON>out<PERSON>, <PERSON>, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/router-devtools'

export const Route = createRootRoute({
  component: () => (
    <>
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="text-xl font-bold text-gray-900">
                E-Connect 2
              </Link>
            </div>
            <div className="flex items-center space-x-8">
              <Link
                to="/"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium [&.active]:text-blue-600 [&.active]:font-semibold"
              >
                Inbox
              </Link>
              <Link
                to="/rules"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium [&.active]:text-blue-600 [&.active]:font-semibold"
              >
                Rules
              </Link>
              <Link
                to="/chat"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium [&.active]:text-blue-600 [&.active]:font-semibold"
              >
                AI Chat
              </Link>
              <Link
                to="/settings"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium [&.active]:text-blue-600 [&.active]:font-semibold"
              >
                Settings
              </Link>
              <Link
                to="/about"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium [&.active]:text-blue-600 [&.active]:font-semibold"
              >
                About
              </Link>
            </div>
          </div>
        </div>
      </nav>
      <main className="min-h-screen bg-gray-50">
        <Outlet />
      </main>
      <TanStackRouterDevtools />
    </>
  ),
})
