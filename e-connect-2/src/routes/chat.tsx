import { createFileRoute } from '@tanstack/react-router'
import { Container } from '@/components/ui/Container'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export const Route = createFileRoute('/chat')({
  component: Chat,
})

function Chat() {
  return (
    <Container className="py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">AI Assistant</h1>
        <p className="text-gray-600 mt-2">
          Get help managing your emails with our AI assistant
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle>Chat with AI Assistant</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <div className="flex-1 bg-gray-50 rounded-lg p-4 mb-4 overflow-y-auto">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      AI
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <p className="text-gray-800">
                        Hello! I'm your AI email assistant. I can help you manage your emails, create rules, and answer questions about your inbox. How can I help you today?
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 justify-end">
                    <div className="bg-blue-600 text-white rounded-lg p-3 shadow-sm">
                      <p>
                        Can you help me create a rule to automatically archive newsletters?
                      </p>
                    </div>
                    <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      You
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      AI
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <p className="text-gray-800">
                        Absolutely! I can help you create a rule to automatically archive newsletters. This rule will identify emails that look like newsletters and move them to your archive folder. Would you like me to create this rule for you?
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Input
                  placeholder="Type your message..."
                  className="flex-1"
                />
                <Button>Send</Button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                Create Email Rule
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Analyze Inbox
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Bulk Archive
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Email Summary
              </Button>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Chats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100">
                  <p className="font-medium">Newsletter Management</p>
                  <p className="text-gray-500 text-xs">2 hours ago</p>
                </div>
                <div className="p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100">
                  <p className="font-medium">Email Organization</p>
                  <p className="text-gray-500 text-xs">Yesterday</p>
                </div>
                <div className="p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100">
                  <p className="font-medium">Spam Detection</p>
                  <p className="text-gray-500 text-xs">3 days ago</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Container>
  )
}
