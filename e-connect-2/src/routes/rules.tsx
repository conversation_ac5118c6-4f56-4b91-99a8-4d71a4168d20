import { createFileRoute } from '@tanstack/react-router'
import { Container } from '@/components/ui/Container'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export const Route = createFileRoute('/rules')({
  component: Rules,
})

function Rules() {
  return (
    <Container className="py-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Email Rules</h1>
            <p className="text-gray-600 mt-2">
              Automate your email management with AI-powered rules
            </p>
          </div>
          <Button>Create Rule</Button>
        </div>
      </div>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Archive Newsletters</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Automatically archive all newsletter emails to keep your inbox clean.
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-green-600 font-medium">Active</span>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Edit</Button>
                <Button variant="outline" size="sm">Disable</Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Label Work Emails</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Automatically label emails from work domains with "Work" label.
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-green-600 font-medium">Active</span>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Edit</Button>
                <Button variant="outline" size="sm">Disable</Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Forward Important Emails</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Forward emails marked as important to your mobile device.
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500 font-medium">Inactive</span>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Edit</Button>
                <Button variant="outline" size="sm">Enable</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
