{"name": "e-connect-2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.125.6", "@tanstack/router-devtools": "^1.125.6", "@tanstack/router-vite-plugin": "^1.125.6", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "msw": "^2.10.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "msw": {"workerDirectory": ["public"]}}