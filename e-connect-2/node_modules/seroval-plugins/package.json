{"name": "seroval-plugins", "type": "module", "version": "1.3.2", "files": ["dist", "web"], "engines": {"node": ">=10"}, "license": "MIT", "keywords": ["pridepack"], "devDependencies": {"@types/node": "^22.15.12", "@vitest/ui": "^3.1.3", "pridepack": "2.6.4", "seroval": "1.3.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "peerDependencies": {"seroval": "^1.0"}, "scripts": {"prepublishOnly": "pridepack clean && pridepack build", "build": "pridepack build", "type-check": "pridepack check", "clean": "pridepack clean", "watch": "pridepack watch", "start": "pridepack start", "dev": "pridepack dev", "test": "vitest", "test:ui": "vitest --ui"}, "private": false, "description": "Stringify JS values", "repository": {"url": "https://github.com/lxsmnsyc/seroval.git", "type": "git"}, "homepage": "https://github.com/lxsmnsyc/seroval/tree/main/packages/plugins", "bugs": {"url": "https://github.com/lxsmnsyc/seroval/issues"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "exports": {"./web": {"types": "./dist/types/web/index.d.ts", "development": {"require": "./dist/cjs/development/web.cjs", "import": "./dist/esm/development/web.mjs"}, "require": "./dist/cjs/production/web.cjs", "import": "./dist/esm/production/web.mjs"}}, "typesVersions": {"*": {"web": ["./dist/types/web/index.d.ts"]}}, "gitHead": "c1e47dd1233aaec1d4fae82c5776c31d5850def9"}