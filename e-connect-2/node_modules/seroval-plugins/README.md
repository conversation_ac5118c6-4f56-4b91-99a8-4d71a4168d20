# `seroval-plugins`

> Plugins for `seroval`

[![NPM](https://img.shields.io/npm/v/seroval.svg)](https://www.npmjs.com/package/seroval) [![JavaScript Style Guide](https://badgen.net/badge/code%20style/airbnb/ff5a5f?icon=airbnb)](https://github.com/airbnb/javascript)

## Install

```bash
npm install --save seroval-plugins
```

```bash
yarn add seroval-plugins
```

```bash
pnpm add seroval-plugins
```

## Features

### `seroval-plugins/web`

- [`AbortSignal`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal)
- [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob)
- [`CustomEvent`](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent)
- [`DOMException`](https://developer.mozilla.org/en-US/docs/Web/API/DOMException)
- [`Event`](https://developer.mozilla.org/en-US/docs/Web/API/Event)
- [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File)
- [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData)
- [`ImageData`](https://developer.mozilla.org/en-US/docs/Web/API/ImageData)
- [`Headers`](https://developer.mozilla.org/en-US/docs/Web/API/Headers)
- [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream)
- [`Request`](https://developer.mozilla.org/en-US/docs/Web/API/Request)
- [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)
- [`URLSearchParams`](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams)
- [`URL`](https://developer.mozilla.org/en-US/docs/Web/API/URL)

## Sponsors

![Sponsors](https://github.com/lxsmnsyc/sponsors/blob/main/sponsors.svg?raw=true)

## License

MIT © [lxsmnsyc](https://github.com/lxsmnsyc)
