{"version": 3, "sources": ["../../../web/index.ts", "../../../web/abort-signal.ts", "../../../web/blob.ts", "../../../web/custom-event.ts", "../../../web/dom-exception.ts", "../../../web/event.ts", "../../../web/file.ts", "../../../web/form-data.ts", "../../../web/headers.ts", "../../../web/image-data.ts", "../../../web/readable-stream.ts", "../../../web/request.ts", "../../../web/response.ts", "../../../web/url.ts", "../../../web/url-search-params.ts"], "sourcesContent": ["export { default as AbortSignalPlugin } from './abort-signal';\nexport { default as BlobPlugin } from './blob';\nexport { default as CustomEventPlugin } from './custom-event';\nexport { default as DOMExceptionPlugin } from './dom-exception';\nexport { default as EventPlugin } from './event';\nexport { default as FilePlugin } from './file';\nexport { default as FormDataPlugin } from './form-data';\nexport { default as HeadersPlugin } from './headers';\nexport { default as ImageDataPlugin } from './image-data';\nexport { default as ReadableStreamPlugin } from './readable-stream';\nexport { default as RequestPlugin } from './request';\nexport { default as ResponsePlugin } from './response';\nexport { default as URLPlugin } from './url';\nexport { default as URLSearchParamsPlugin } from './url-search-params';\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nfunction resolveAbortSignalResult(\n  this: AbortSignal,\n  resolve: (value: any) => void,\n): void {\n  resolve(this.reason);\n}\n\nfunction resolveAbortSignal(\n  this: AbortSignal,\n  resolve: (value: any) => void,\n): void {\n  this.addEventListener('abort', resolveAbortSignalResult.bind(this, resolve), {\n    once: true,\n  });\n}\n\nexport function abortSignalToPromise(signal: AbortSignal): Promise<any> {\n  return new Promise(resolveAbortSignal.bind(signal));\n}\n\nclass AbortSignalController {\n  controller = new AbortController();\n}\n\nconst AbortSignalControllerPlugin = createPlugin<\n  AbortSignalController,\n  undefined\n>({\n  tag: 'seroval-plugins/web/AbortSignalController',\n  test(value) {\n    // We didn't actually use the AbortController class\n    // directly because of some assumptions\n    return value instanceof AbortSignalController;\n  },\n  parse: {\n    stream() {\n      return undefined;\n    },\n  },\n  serialize(_node) {\n    return 'new AbortController';\n  },\n  deserialize(_node) {\n    return new AbortSignalController();\n  },\n});\n\ninterface AbortSignalAbortNode {\n  controller: SerovalNode;\n  reason: SerovalNode;\n}\n\nclass AbortSignalAbort {\n  constructor(\n    public controller: AbortSignalController,\n    public reason: unknown,\n  ) {}\n}\n\nconst AbortSignalAbortPlugin = createPlugin<\n  AbortSignalAbort,\n  AbortSignalAbortNode\n>({\n  extends: [AbortSignalControllerPlugin],\n  tag: 'seroval-plugins/web/AbortSignalAbort',\n  test(value) {\n    return value instanceof AbortSignalAbort;\n  },\n  parse: {\n    stream(value, ctx) {\n      return {\n        controller: ctx.parse(value.controller),\n        reason: ctx.parse(value.reason),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      ctx.serialize(node.controller) +\n      '.abort(' +\n      ctx.serialize(node.reason) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    const controller = ctx.deserialize(\n      node.controller,\n    ) as AbortSignalController;\n    const reason = ctx.deserialize(node.reason);\n    controller.controller.abort(reason);\n    return new AbortSignalAbort(controller, reason);\n  },\n});\n\nconst enum AbortSignalState {\n  Pending = 0,\n  Aborted = 1,\n  Streaming = 2,\n}\n\ntype AbortSignalNode =\n  | { type: AbortSignalState.Pending }\n  | { type: AbortSignalState.Aborted; reason: SerovalNode }\n  | { type: AbortSignalState.Streaming; controller: SerovalNode };\n\nconst AbortSignalPlugin = createPlugin<AbortSignal, AbortSignalNode>({\n  tag: 'seroval-plugins/web/AbortSignal',\n  extends: [AbortSignalAbortPlugin],\n  test(value) {\n    if (typeof AbortSignal === 'undefined') {\n      return false;\n    }\n    return value instanceof AbortSignal;\n  },\n  parse: {\n    sync(value, ctx) {\n      if (value.aborted) {\n        return {\n          type: AbortSignalState.Aborted,\n          reason: ctx.parse(value.reason),\n        };\n      }\n      return {\n        type: AbortSignalState.Pending,\n      };\n    },\n    async async(value, ctx) {\n      if (value.aborted) {\n        return {\n          type: AbortSignalState.Aborted,\n          reason: await ctx.parse(value.reason),\n        };\n      }\n      const result = await abortSignalToPromise(value);\n      return {\n        type: AbortSignalState.Aborted,\n        reason: await ctx.parse(result),\n      };\n    },\n    stream(value, ctx) {\n      if (value.aborted) {\n        return {\n          type: AbortSignalState.Aborted,\n          reason: ctx.parse(value.reason),\n        };\n      }\n      const controller = new AbortSignalController();\n\n      ctx.pushPendingState();\n      value.addEventListener(\n        'abort',\n        () => {\n          const result = ctx.parseWithError(\n            new AbortSignalAbort(controller, value.reason),\n          );\n          if (result) {\n            ctx.onParse(result);\n          }\n          ctx.popPendingState();\n        },\n        { once: true },\n      );\n\n      return {\n        type: AbortSignalState.Streaming,\n        controller: ctx.parse(controller),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    if (node.type === AbortSignalState.Pending) {\n      return '(new AbortController).signal';\n    }\n    if (node.type === AbortSignalState.Aborted) {\n      return 'AbortSignal.abort(' + ctx.serialize(node.reason) + ')';\n    }\n    return '(' + ctx.serialize(node.controller) + ').signal';\n  },\n  deserialize(node, ctx) {\n    if (node.type === AbortSignalState.Pending) {\n      const controller = new AbortController();\n      return controller.signal;\n    }\n    if (node.type === AbortSignalState.Aborted) {\n      return AbortSignal.abort(ctx.deserialize(node.reason));\n    }\n    const controller = ctx.deserialize(\n      node.controller,\n    ) as AbortSignalController;\n    return controller.controller.signal;\n  },\n});\n\nexport default AbortSignalPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\ninterface BlobNode {\n  type: SerovalNode;\n  buffer: SerovalNode;\n}\n\nconst BlobPlugin = /* @__PURE__ */ createPlugin<Blob, BlobNode>({\n  tag: 'seroval-plugins/web/Blob',\n  test(value) {\n    if (typeof Blob === 'undefined') {\n      return false;\n    }\n    return value instanceof Blob;\n  },\n  parse: {\n    async async(value, ctx) {\n      return {\n        type: await ctx.parse(value.type),\n        buffer: await ctx.parse(await value.arrayBuffer()),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new Blob([' +\n      ctx.serialize(node.buffer) +\n      '],{type:' +\n      ctx.serialize(node.type) +\n      '})'\n    );\n  },\n  deserialize(node, ctx) {\n    return new Blob([ctx.deserialize(node.buffer) as ArrayBuffer], {\n      type: ctx.deserialize(node.type) as string,\n    });\n  },\n});\n\nexport default BlobPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nfunction createCustomEventOptions(current: CustomEvent): CustomEventInit {\n  return {\n    detail: current.detail as unknown,\n    bubbles: current.bubbles,\n    cancelable: current.cancelable,\n    composed: current.composed,\n  };\n}\n\ninterface CustomEventNode {\n  type: SerovalNode;\n  options: SerovalNode;\n}\n\nconst CustomEventPlugin = /* @__PURE__ */ createPlugin<\n  CustomEvent,\n  CustomEventNode\n>({\n  tag: 'seroval-plugins/web/CustomEvent',\n  test(value) {\n    if (typeof CustomEvent === 'undefined') {\n      return false;\n    }\n    return value instanceof CustomEvent;\n  },\n  parse: {\n    sync(value, ctx) {\n      return {\n        type: ctx.parse(value.type),\n        options: ctx.parse(createCustomEventOptions(value)),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        type: await ctx.parse(value.type),\n        options: await ctx.parse(createCustomEventOptions(value)),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        type: ctx.parse(value.type),\n        options: ctx.parse(createCustomEventOptions(value)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new CustomEvent(' +\n      ctx.serialize(node.type) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new CustomEvent(\n      ctx.deserialize(node.type) as string,\n      ctx.deserialize(node.options) as CustomEventInit,\n    );\n  },\n});\n\nexport default CustomEventPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\ninterface DOMExceptionNode {\n  name: SerovalNode;\n  message: SerovalNode;\n}\n\nconst DOMExceptionPlugin = /* @__PURE__ */ createPlugin<\n  DOMException,\n  DOMExceptionNode\n>({\n  tag: 'seroval-plugins/web/DOMException',\n  test(value) {\n    if (typeof DOMException === 'undefined') {\n      return false;\n    }\n    return value instanceof DOMException;\n  },\n  parse: {\n    sync(value, ctx) {\n      return {\n        name: ctx.parse(value.name),\n        message: ctx.parse(value.message),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        name: await ctx.parse(value.name),\n        message: await ctx.parse(value.message),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        name: ctx.parse(value.name),\n        message: ctx.parse(value.message),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new DOMException(' +\n      ctx.serialize(node.message) +\n      ',' +\n      ctx.serialize(node.name) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new DOMException(\n      ctx.deserialize(node.message) as string,\n      ctx.deserialize(node.name) as string,\n    );\n  },\n});\n\nexport default DOMExceptionPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nfunction createEventOptions(current: Event): EventInit {\n  return {\n    bubbles: current.bubbles,\n    cancelable: current.cancelable,\n    composed: current.composed,\n  };\n}\n\ninterface EventNode {\n  type: SerovalNode;\n  options: SerovalNode;\n}\n\nconst EventPlugin = /* @__PURE__ */ createPlugin<Event, EventNode>({\n  tag: 'seroval-plugins/web/Event',\n  test(value) {\n    if (typeof Event === 'undefined') {\n      return false;\n    }\n    return value instanceof Event;\n  },\n  parse: {\n    sync(value, ctx) {\n      return {\n        type: ctx.parse(value.type),\n        options: ctx.parse(createEventOptions(value)),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        type: await ctx.parse(value.type),\n        options: await ctx.parse(createEventOptions(value)),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        type: ctx.parse(value.type),\n        options: ctx.parse(createEventOptions(value)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new Event(' +\n      ctx.serialize(node.type) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new Event(\n      ctx.deserialize(node.type) as string,\n      ctx.deserialize(node.options) as EventInit,\n    );\n  },\n});\n\nexport default EventPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\ninterface FileNode {\n  name: SerovalNode;\n  options: SerovalNode;\n  buffer: SerovalNode;\n}\n\nconst FilePlugin = /* @__PURE__ */ createPlugin<File, FileNode>({\n  tag: 'seroval-plugins/web/File',\n  test(value) {\n    if (typeof File === 'undefined') {\n      return false;\n    }\n    return value instanceof File;\n  },\n  parse: {\n    async async(value, ctx) {\n      return {\n        name: await ctx.parse(value.name),\n        options: await ctx.parse({\n          type: value.type,\n          lastModified: value.lastModified,\n        }),\n        buffer: await ctx.parse(await value.arrayBuffer()),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new File([' +\n      ctx.serialize(node.buffer) +\n      '],' +\n      ctx.serialize(node.name) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new File(\n      [ctx.deserialize(node.buffer) as Array<PERSON>uffer],\n      ctx.deserialize(node.name) as string,\n      ctx.deserialize(node.options) as FilePropertyBag,\n    );\n  },\n});\n\nexport default FilePlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\nimport FilePlugin from './file';\n\ntype FormDataInit = [key: string, value: FormDataEntryValue][];\n\nfunction convertFormData(instance: FormData): FormDataInit {\n  const items: FormDataInit = [];\n  // biome-ignore lint/complexity/noForEach: <explanation>\n  instance.forEach((value, key) => {\n    items.push([key, value]);\n  });\n  return items;\n}\n\nconst FORM_DATA_FACTORY = {};\n\nconst FormDataFactoryPlugin = /* @__PURE__ */ createPlugin<object, undefined>({\n  tag: 'seroval-plugins/web/FormDataFactory',\n  test(value) {\n    return value === FORM_DATA_FACTORY;\n  },\n  parse: {\n    sync() {\n      return undefined;\n    },\n    async async() {\n      return await Promise.resolve(undefined);\n    },\n    stream() {\n      return undefined;\n    },\n  },\n  serialize(_node, ctx) {\n    return ctx.createEffectfulFunction(\n      ['e', 'f', 'i', 's', 't'],\n      'f=new FormData;for(i=0,s=e.length;i<s;i++)f.append((t=e[i])[0],t[1]);return f',\n    );\n  },\n  deserialize() {\n    return FORM_DATA_FACTORY;\n  },\n});\n\ninterface FormDataNode {\n  factory: SerovalNode;\n  entries: SerovalNode;\n}\n\nconst FormDataPlugin = /* @__PURE__ */ createPlugin<FormData, FormDataNode>({\n  tag: 'seroval-plugins/web/FormData',\n  extends: [FilePlugin, FormDataFactoryPlugin],\n  test(value) {\n    if (typeof FormData === 'undefined') {\n      return false;\n    }\n    return value instanceof FormData;\n  },\n  parse: {\n    sync(value, ctx) {\n      return {\n        factory: ctx.parse(FORM_DATA_FACTORY),\n        entries: ctx.parse(convertFormData(value)),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        factory: await ctx.parse(FORM_DATA_FACTORY),\n        entries: await ctx.parse(convertFormData(value)),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        factory: ctx.parse(FORM_DATA_FACTORY),\n        entries: ctx.parse(convertFormData(value)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      '(' +\n      ctx.serialize(node.factory) +\n      ')(' +\n      ctx.serialize(node.entries) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    const instance = new FormData();\n    const entries = ctx.deserialize(node.entries) as FormDataInit;\n    for (let i = 0, len = entries.length; i < len; i++) {\n      const entry = entries[i];\n      instance.append(entry[0], entry[1]);\n    }\n    return instance;\n  },\n});\n\nexport default FormDataPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nfunction convertHeaders(instance: Headers): HeadersInit {\n  const items: HeadersInit = [];\n  // biome-ignore lint/complexity/noForEach: <explanation>\n  instance.forEach((value, key) => {\n    items.push([key, value]);\n  });\n  return items;\n}\n\nconst HeadersPlugin = /* @__PURE__ */ createPlugin<Headers, SerovalNode>({\n  tag: 'seroval-plugins/web/Headers',\n  test(value) {\n    if (typeof Headers === 'undefined') {\n      return false;\n    }\n    return value instanceof Headers;\n  },\n  parse: {\n    sync(value, ctx) {\n      return ctx.parse(convertHeaders(value));\n    },\n    async async(value, ctx) {\n      return await ctx.parse(convertHeaders(value));\n    },\n    stream(value, ctx) {\n      return ctx.parse(convertHeaders(value));\n    },\n  },\n  serialize(node, ctx) {\n    return 'new Headers(' + ctx.serialize(node) + ')';\n  },\n  deserialize(node, ctx) {\n    return new Headers(ctx.deserialize(node) as HeadersInit);\n  },\n});\n\nexport default HeadersPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\ninterface ImageDataNode {\n  data: SerovalNode;\n  width: SerovalNode;\n  height: SerovalNode;\n  options: SerovalNode;\n}\n\nconst ImageDataPlugin = /* @__PURE__ */ createPlugin<ImageData, ImageDataNode>({\n  tag: 'seroval-plugins/web/ImageData',\n  test(value) {\n    if (typeof ImageData === 'undefined') {\n      return false;\n    }\n    return value instanceof ImageData;\n  },\n  parse: {\n    sync(value, ctx) {\n      return {\n        data: ctx.parse(value.data),\n        width: ctx.parse(value.width),\n        height: ctx.parse(value.height),\n        options: ctx.parse({\n          colorSpace: value.colorSpace,\n        }),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        data: await ctx.parse(value.data),\n        width: await ctx.parse(value.width),\n        height: await ctx.parse(value.height),\n        options: await ctx.parse({\n          colorSpace: value.colorSpace,\n        }),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        data: ctx.parse(value.data),\n        width: ctx.parse(value.width),\n        height: ctx.parse(value.height),\n        options: ctx.parse({\n          colorSpace: value.colorSpace,\n        }),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new ImageData(' +\n      ctx.serialize(node.data) +\n      ',' +\n      ctx.serialize(node.width) +\n      ',' +\n      ctx.serialize(node.height) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new ImageData(\n      ctx.deserialize(node.data) as Uint8ClampedArray,\n      ctx.deserialize(node.width) as number,\n      ctx.deserialize(node.height) as number,\n      ctx.deserialize(node.options) as ImageDataSettings,\n    );\n  },\n});\n\nexport default ImageDataPlugin;\n", "import type { SerovalNode, Stream } from 'seroval';\nimport { createPlugin, createStream } from 'seroval';\n\nconst READABLE_STREAM_FACTORY = {};\n\nconst ReadableStreamFactoryPlugin = /* @__PURE__ */ createPlugin<\n  object,\n  undefined\n>({\n  tag: 'seroval-plugins/web/ReadableStreamFactory',\n  test(value) {\n    return value === READABLE_STREAM_FACTORY;\n  },\n  parse: {\n    sync() {\n      return undefined;\n    },\n    async async() {\n      return await Promise.resolve(undefined);\n    },\n    stream() {\n      return undefined;\n    },\n  },\n  serialize(_node, ctx) {\n    return ctx.createFunction(\n      ['d'],\n      'new ReadableStream({start:' +\n        ctx.createEffectfulFunction(\n          ['c'],\n          'd.on({next:' +\n            ctx.createEffectfulFunction(['v'], 'c.enqueue(v)') +\n            ',throw:' +\n            ctx.createEffectfulFunction(['v'], 'c.error(v)') +\n            ',return:' +\n            ctx.createEffectfulFunction([], 'c.close()') +\n            '})',\n        ) +\n        '})',\n    );\n  },\n  deserialize() {\n    return READABLE_STREAM_FACTORY;\n  },\n});\n\nfunction toStream<T>(value: ReadableStream<T>): Stream<T | undefined> {\n  const stream = createStream<T | undefined>();\n\n  const reader = value.getReader();\n\n  async function push(): Promise<void> {\n    try {\n      const result = await reader.read();\n      if (result.done) {\n        stream.return(result.value);\n      } else {\n        stream.next(result.value);\n        await push();\n      }\n    } catch (error) {\n      stream.throw(error);\n    }\n  }\n\n  push().catch(() => {\n    //\n  });\n\n  return stream;\n}\n\ninterface ReadableStreamNode {\n  factory: SerovalNode;\n  stream: SerovalNode;\n}\n\nconst ReadableStreamPlugin = /* @__PURE__ */ createPlugin<\n  ReadableStream,\n  ReadableStreamNode\n>({\n  tag: 'seroval/plugins/web/ReadableStream',\n  extends: [ReadableStreamFactoryPlugin],\n  test(value) {\n    if (typeof ReadableStream === 'undefined') {\n      return false;\n    }\n    return value instanceof ReadableStream;\n  },\n  parse: {\n    sync(_value, ctx) {\n      return {\n        factory: ctx.parse(READABLE_STREAM_FACTORY),\n        stream: ctx.parse(createStream()),\n      };\n    },\n    async async(value, ctx) {\n      return {\n        factory: await ctx.parse(READABLE_STREAM_FACTORY),\n        stream: await ctx.parse(toStream(value)),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        factory: ctx.parse(READABLE_STREAM_FACTORY),\n        stream: ctx.parse(toStream(value)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      '(' +\n      ctx.serialize(node.factory) +\n      ')(' +\n      ctx.serialize(node.stream) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    const stream = ctx.deserialize(node.stream) as Stream<any>;\n    return new ReadableStream({\n      start(controller): void {\n        stream.on({\n          next(value) {\n            controller.enqueue(value);\n          },\n          throw(value) {\n            controller.error(value);\n          },\n          return() {\n            controller.close();\n          },\n        });\n      },\n    });\n  },\n});\n\nexport default ReadableStreamPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\nimport ReadableStreamPlugin from './readable-stream';\nimport HeadersPlugin from './headers';\n\nfunction createRequestOptions(\n  current: Request,\n  body: ArrayBuffer | ReadableStream | null,\n): RequestInit {\n  return {\n    body,\n    cache: current.cache,\n    credentials: current.credentials,\n    headers: current.headers,\n    integrity: current.integrity,\n    keepalive: current.keepalive,\n    method: current.method,\n    mode: current.mode,\n    redirect: current.redirect,\n    referrer: current.referrer,\n    referrerPolicy: current.referrerPolicy,\n  };\n}\n\ninterface RequestNode {\n  url: SerovalNode;\n  options: SerovalNode;\n}\n\nconst RequestPlugin = /* @__PURE__ */ createPlugin<Request, RequestNode>({\n  tag: 'seroval-plugins/web/Request',\n  extends: [ReadableStreamPlugin, HeadersPlugin],\n  test(value) {\n    if (typeof Request === 'undefined') {\n      return false;\n    }\n    return value instanceof Request;\n  },\n  parse: {\n    async async(value, ctx) {\n      return {\n        url: await ctx.parse(value.url),\n        options: await ctx.parse(\n          createRequestOptions(\n            value,\n            value.body ? await value.clone().arrayBuffer() : null,\n          ),\n        ),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        url: ctx.parse(value.url),\n        options: ctx.parse(createRequestOptions(value, value.clone().body)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new Request(' +\n      ctx.serialize(node.url) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new Request(\n      ctx.deserialize(node.url) as string,\n      ctx.deserialize(node.options) as RequestInit,\n    );\n  },\n});\n\nexport default RequestPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\nimport ReadableStreamPlugin from './readable-stream';\nimport HeadersPlugin from './headers';\n\nfunction createResponseOptions(current: Response): ResponseInit {\n  return {\n    headers: current.headers,\n    status: current.status,\n    statusText: current.statusText,\n  };\n}\n\ninterface ResponseNode {\n  body: SerovalNode;\n  options: SerovalNode;\n}\n\nconst ResponsePlugin = /* @__PURE__ */ createPlugin<Response, ResponseNode>({\n  tag: 'seroval-plugins/web/Response',\n  extends: [ReadableStreamPlugin, HeadersPlugin],\n  test(value) {\n    if (typeof Response === 'undefined') {\n      return false;\n    }\n    return value instanceof Response;\n  },\n  parse: {\n    async async(value, ctx) {\n      return {\n        body: await ctx.parse(\n          value.body ? await value.clone().arrayBuffer() : null,\n        ),\n        options: await ctx.parse(createResponseOptions(value)),\n      };\n    },\n    stream(value, ctx) {\n      return {\n        body: ctx.parse(value.clone().body),\n        options: ctx.parse(createResponseOptions(value)),\n      };\n    },\n  },\n  serialize(node, ctx) {\n    return (\n      'new Response(' +\n      ctx.serialize(node.body) +\n      ',' +\n      ctx.serialize(node.options) +\n      ')'\n    );\n  },\n  deserialize(node, ctx) {\n    return new Response(\n      ctx.deserialize(node.body) as BodyInit,\n      ctx.deserialize(node.options) as ResponseInit,\n    );\n  },\n});\n\nexport default ResponsePlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nconst URLPlugin = /* @__PURE__ */ createPlugin<URL, SerovalNode>({\n  tag: 'seroval-plugins/web/URL',\n  test(value) {\n    if (typeof URL === 'undefined') {\n      return false;\n    }\n    return value instanceof URL;\n  },\n  parse: {\n    sync(value, ctx) {\n      return ctx.parse(value.href);\n    },\n    async async(value, ctx) {\n      return await ctx.parse(value.href);\n    },\n    stream(value, ctx) {\n      return ctx.parse(value.href);\n    },\n  },\n  serialize(node, ctx) {\n    return 'new URL(' + ctx.serialize(node) + ')';\n  },\n  deserialize(node, ctx) {\n    return new URL(ctx.deserialize(node) as string);\n  },\n});\n\nexport default URLPlugin;\n", "import type { SerovalNode } from 'seroval';\nimport { createPlugin } from 'seroval';\n\nconst URLSearchParamsPlugin = /* @__PURE__ */ createPlugin<\n  URLSearchParams,\n  SerovalNode\n>({\n  tag: 'seroval-plugins/web/URLSearchParams',\n  test(value) {\n    if (typeof URLSearchParams === 'undefined') {\n      return false;\n    }\n    return value instanceof URLSearchParams;\n  },\n  parse: {\n    sync(value, ctx) {\n      return ctx.parse(value.toString());\n    },\n    async async(value, ctx) {\n      return await ctx.parse(value.toString());\n    },\n    stream(value, ctx) {\n      return ctx.parse(value.toString());\n    },\n  },\n  serialize(node, ctx) {\n    return 'new URLSearchParams(' + ctx.serialize(node) + ')';\n  },\n  deserialize(node, ctx) {\n    return new URLSearchParams(ctx.deserialize(node) as string);\n  },\n});\n\nexport default URLSearchParamsPlugin;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,qBAA6B;AAE7B,SAAS,yBAEP,SACM;AACN,UAAQ,KAAK,MAAM;AACrB;AAEA,SAAS,mBAEP,SACM;AACN,OAAK,iBAAiB,SAAS,yBAAyB,KAAK,MAAM,OAAO,GAAG;AAAA,IAC3E,MAAM;AAAA,EACR,CAAC;AACH;AAEO,SAAS,qBAAqB,QAAmC;AACtE,SAAO,IAAI,QAAQ,mBAAmB,KAAK,MAAM,CAAC;AACpD;AAEA,IAAM,wBAAN,MAA4B;AAAA,EAA5B;AACE,sBAAa,IAAI,gBAAgB;AAAA;AACnC;AAEA,IAAM,kCAA8B,6BAGlC;AAAA,EACA,KAAK;AAAA,EACL,KAAK,OAAO;AAGV,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,IAAI,sBAAsB;AAAA,EACnC;AACF,CAAC;AAOD,IAAM,mBAAN,MAAuB;AAAA,EACrB,YACS,YACA,QACP;AAFO;AACA;AAAA,EACN;AACL;AAEA,IAAM,6BAAyB,6BAG7B;AAAA,EACA,SAAS,CAAC,2BAA2B;AAAA,EACrC,KAAK;AAAA,EACL,KAAK,OAAO;AACV,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,YAAY,IAAI,MAAM,MAAM,UAAU;AAAA,QACtC,QAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,IAAI,UAAU,KAAK,UAAU,IAC7B,YACA,IAAI,UAAU,KAAK,MAAM,IACzB;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK;AAAA,IACP;AACA,UAAM,SAAS,IAAI,YAAY,KAAK,MAAM;AAC1C,eAAW,WAAW,MAAM,MAAM;AAClC,WAAO,IAAI,iBAAiB,YAAY,MAAM;AAAA,EAChD;AACF,CAAC;AAaD,IAAM,wBAAoB,6BAA2C;AAAA,EACnE,KAAK;AAAA,EACL,SAAS,CAAC,sBAAsB;AAAA,EAChC,KAAK,OAAO;AACV,QAAI,OAAO,gBAAgB,aAAa;AACtC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,UAAI,MAAM,SAAS;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,QAChC;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,UAAI,MAAM,SAAS;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM;AAAA,QACtC;AAAA,MACF;AACA,YAAM,SAAS,MAAM,qBAAqB,KAAK;AAC/C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ,MAAM,IAAI,MAAM,MAAM;AAAA,MAChC;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,UAAI,MAAM,SAAS;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,QAChC;AAAA,MACF;AACA,YAAM,aAAa,IAAI,sBAAsB;AAE7C,UAAI,iBAAiB;AACrB,YAAM;AAAA,QACJ;AAAA,QACA,MAAM;AACJ,gBAAM,SAAS,IAAI;AAAA,YACjB,IAAI,iBAAiB,YAAY,MAAM,MAAM;AAAA,UAC/C;AACA,cAAI,QAAQ;AACV,gBAAI,QAAQ,MAAM;AAAA,UACpB;AACA,cAAI,gBAAgB;AAAA,QACtB;AAAA,QACA,EAAE,MAAM,KAAK;AAAA,MACf;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,YAAY,IAAI,MAAM,UAAU;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,QAAI,KAAK,SAAS,iBAA0B;AAC1C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,SAAS,iBAA0B;AAC1C,aAAO,uBAAuB,IAAI,UAAU,KAAK,MAAM,IAAI;AAAA,IAC7D;AACA,WAAO,MAAM,IAAI,UAAU,KAAK,UAAU,IAAI;AAAA,EAChD;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,QAAI,KAAK,SAAS,iBAA0B;AAC1C,YAAMA,cAAa,IAAI,gBAAgB;AACvC,aAAOA,YAAW;AAAA,IACpB;AACA,QAAI,KAAK,SAAS,iBAA0B;AAC1C,aAAO,YAAY,MAAM,IAAI,YAAY,KAAK,MAAM,CAAC;AAAA,IACvD;AACA,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK;AAAA,IACP;AACA,WAAO,WAAW,WAAW;AAAA,EAC/B;AACF,CAAC;AAED,IAAO,uBAAQ;;;ACnMf,IAAAC,kBAA6B;AAO7B,IAAM,aAA6B,kDAA6B;AAAA,EAC9D,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,SAAS,aAAa;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,YAAY,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,eACA,IAAI,UAAU,KAAK,MAAM,IACzB,aACA,IAAI,UAAU,KAAK,IAAI,IACvB;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI,KAAK,CAAC,IAAI,YAAY,KAAK,MAAM,CAAgB,GAAG;AAAA,MAC7D,MAAM,IAAI,YAAY,KAAK,IAAI;AAAA,IACjC,CAAC;AAAA,EACH;AACF,CAAC;AAED,IAAO,eAAQ;;;ACvCf,IAAAC,kBAA6B;AAE7B,SAAS,yBAAyB,SAAuC;AACvE,SAAO;AAAA,IACL,QAAQ,QAAQ;AAAA,IAChB,SAAS,QAAQ;AAAA,IACjB,YAAY,QAAQ;AAAA,IACpB,UAAU,QAAQ;AAAA,EACpB;AACF;AAOA,IAAM,oBAAoC,kDAGxC;AAAA,EACA,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,gBAAgB,aAAa;AACtC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,yBAAyB,KAAK,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,SAAS,MAAM,IAAI,MAAM,yBAAyB,KAAK,CAAC;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,yBAAyB,KAAK,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,qBACA,IAAI,UAAU,KAAK,IAAI,IACvB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,uBAAQ;;;AChEf,IAAAC,kBAA6B;AAO7B,IAAM,qBAAqC,kDAGzC;AAAA,EACA,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,iBAAiB,aAAa;AACvC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,MAAM,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,SAAS,MAAM,IAAI,MAAM,MAAM,OAAO;AAAA,MACxC;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,MAAM,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,sBACA,IAAI,UAAU,KAAK,OAAO,IAC1B,MACA,IAAI,UAAU,KAAK,IAAI,IACvB;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,OAAO;AAAA,MAC5B,IAAI,YAAY,KAAK,IAAI;AAAA,IAC3B;AAAA,EACF;AACF,CAAC;AAED,IAAO,wBAAQ;;;ACvDf,IAAAC,kBAA6B;AAE7B,SAAS,mBAAmB,SAA2B;AACrD,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,YAAY,QAAQ;AAAA,IACpB,UAAU,QAAQ;AAAA,EACpB;AACF;AAOA,IAAM,cAA8B,kDAA+B;AAAA,EACjE,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,mBAAmB,KAAK,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,SAAS,MAAM,IAAI,MAAM,mBAAmB,KAAK,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,SAAS,IAAI,MAAM,mBAAmB,KAAK,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,eACA,IAAI,UAAU,KAAK,IAAI,IACvB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,gBAAQ;;;AC5Df,IAAAC,kBAA6B;AAQ7B,IAAM,aAA6B,kDAA6B;AAAA,EAC9D,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,SAAS,aAAa;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,SAAS,MAAM,IAAI,MAAM;AAAA,UACvB,MAAM,MAAM;AAAA,UACZ,cAAc,MAAM;AAAA,QACtB,CAAC;AAAA,QACD,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,YAAY,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,eACA,IAAI,UAAU,KAAK,MAAM,IACzB,OACA,IAAI,UAAU,KAAK,IAAI,IACvB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,CAAC,IAAI,YAAY,KAAK,MAAM,CAAgB;AAAA,MAC5C,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,eAAQ;;;AChDf,IAAAC,kBAA6B;AAK7B,SAAS,gBAAgB,UAAkC;AACzD,QAAM,QAAsB,CAAC;AAE7B,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,EACzB,CAAC;AACD,SAAO;AACT;AAEA,IAAM,oBAAoB,CAAC;AAE3B,IAAM,wBAAwC,kDAAgC;AAAA,EAC5E,KAAK;AAAA,EACL,KAAK,OAAO;AACV,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AACL,aAAO;AAAA,IACT;AAAA,IACA,MAAM,QAAQ;AACZ,aAAO,MAAM,QAAQ,QAAQ,MAAS;AAAA,IACxC;AAAA,IACA,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU,OAAO,KAAK;AACpB,WAAO,IAAI;AAAA,MACT,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO;AAAA,EACT;AACF,CAAC;AAOD,IAAM,iBAAiC,kDAAqC;AAAA,EAC1E,KAAK;AAAA,EACL,SAAS,CAAC,cAAY,qBAAqB;AAAA,EAC3C,KAAK,OAAO;AACV,QAAI,OAAO,aAAa,aAAa;AACnC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO;AAAA,QACL,SAAS,IAAI,MAAM,iBAAiB;AAAA,QACpC,SAAS,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,SAAS,MAAM,IAAI,MAAM,iBAAiB;AAAA,QAC1C,SAAS,MAAM,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,SAAS,IAAI,MAAM,iBAAiB;AAAA,QACpC,SAAS,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B,OACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,UAAM,WAAW,IAAI,SAAS;AAC9B,UAAM,UAAU,IAAI,YAAY,KAAK,OAAO;AAC5C,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,YAAM,QAAQ,QAAQ,CAAC;AACvB,eAAS,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AACF,CAAC;AAED,IAAO,oBAAQ;;;ACjGf,IAAAC,kBAA6B;AAE7B,SAAS,eAAe,UAAgC;AACtD,QAAM,QAAqB,CAAC;AAE5B,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,EACzB,CAAC;AACD,SAAO;AACT;AAEA,IAAM,gBAAgC,kDAAmC;AAAA,EACvE,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,YAAY,aAAa;AAClC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO,IAAI,MAAM,eAAe,KAAK,CAAC;AAAA,IACxC;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO,MAAM,IAAI,MAAM,eAAe,KAAK,CAAC;AAAA,IAC9C;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO,IAAI,MAAM,eAAe,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WAAO,iBAAiB,IAAI,UAAU,IAAI,IAAI;AAAA,EAChD;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI,QAAQ,IAAI,YAAY,IAAI,CAAgB;AAAA,EACzD;AACF,CAAC;AAED,IAAO,kBAAQ;;;ACtCf,IAAAC,kBAA6B;AAS7B,IAAM,kBAAkC,kDAAuC;AAAA,EAC7E,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,cAAc,aAAa;AACpC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,OAAO,IAAI,MAAM,MAAM,KAAK;AAAA,QAC5B,QAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,QAC9B,SAAS,IAAI,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAChC,OAAO,MAAM,IAAI,MAAM,MAAM,KAAK;AAAA,QAClC,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM;AAAA,QACpC,SAAS,MAAM,IAAI,MAAM;AAAA,UACvB,YAAY,MAAM;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC1B,OAAO,IAAI,MAAM,MAAM,KAAK;AAAA,QAC5B,QAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,QAC9B,SAAS,IAAI,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,mBACA,IAAI,UAAU,KAAK,IAAI,IACvB,MACA,IAAI,UAAU,KAAK,KAAK,IACxB,MACA,IAAI,UAAU,KAAK,MAAM,IACzB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB,IAAI,YAAY,KAAK,KAAK;AAAA,MAC1B,IAAI,YAAY,KAAK,MAAM;AAAA,MAC3B,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,qBAAQ;;;ACxEf,IAAAC,mBAA2C;AAE3C,IAAM,0BAA0B,CAAC;AAEjC,IAAM,8BAA8C,mDAGlD;AAAA,EACA,KAAK;AAAA,EACL,KAAK,OAAO;AACV,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AACL,aAAO;AAAA,IACT;AAAA,IACA,MAAM,QAAQ;AACZ,aAAO,MAAM,QAAQ,QAAQ,MAAS;AAAA,IACxC;AAAA,IACA,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU,OAAO,KAAK;AACpB,WAAO,IAAI;AAAA,MACT,CAAC,GAAG;AAAA,MACJ,+BACE,IAAI;AAAA,QACF,CAAC,GAAG;AAAA,QACJ,gBACE,IAAI,wBAAwB,CAAC,GAAG,GAAG,cAAc,IACjD,YACA,IAAI,wBAAwB,CAAC,GAAG,GAAG,YAAY,IAC/C,aACA,IAAI,wBAAwB,CAAC,GAAG,WAAW,IAC3C;AAAA,MACJ,IACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO;AAAA,EACT;AACF,CAAC;AAED,SAAS,SAAY,OAAiD;AACpE,QAAM,aAAS,+BAA4B;AAE3C,QAAM,SAAS,MAAM,UAAU;AAE/B,iBAAe,OAAsB;AACnC,QAAI;AACF,YAAM,SAAS,MAAM,OAAO,KAAK;AACjC,UAAI,OAAO,MAAM;AACf,eAAO,OAAO,OAAO,KAAK;AAAA,MAC5B,OAAO;AACL,eAAO,KAAK,OAAO,KAAK;AACxB,cAAM,KAAK;AAAA,MACb;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,OAAK,EAAE,MAAM,MAAM;AAAA,EAEnB,CAAC;AAED,SAAO;AACT;AAOA,IAAM,uBAAuC,mDAG3C;AAAA,EACA,KAAK;AAAA,EACL,SAAS,CAAC,2BAA2B;AAAA,EACrC,KAAK,OAAO;AACV,QAAI,OAAO,mBAAmB,aAAa;AACzC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,QAAQ,KAAK;AAChB,aAAO;AAAA,QACL,SAAS,IAAI,MAAM,uBAAuB;AAAA,QAC1C,QAAQ,IAAI,UAAM,+BAAa,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,SAAS,MAAM,IAAI,MAAM,uBAAuB;AAAA,QAChD,QAAQ,MAAM,IAAI,MAAM,SAAS,KAAK,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,SAAS,IAAI,MAAM,uBAAuB;AAAA,QAC1C,QAAQ,IAAI,MAAM,SAAS,KAAK,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B,OACA,IAAI,UAAU,KAAK,MAAM,IACzB;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,UAAM,SAAS,IAAI,YAAY,KAAK,MAAM;AAC1C,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,YAAkB;AACtB,eAAO,GAAG;AAAA,UACR,KAAK,OAAO;AACV,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,UACA,MAAM,OAAO;AACX,uBAAW,MAAM,KAAK;AAAA,UACxB;AAAA,UACA,SAAS;AACP,uBAAW,MAAM;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AAED,IAAO,0BAAQ;;;ACzIf,IAAAC,mBAA6B;AAI7B,SAAS,qBACP,SACA,MACa;AACb,SAAO;AAAA,IACL;AAAA,IACA,OAAO,QAAQ;AAAA,IACf,aAAa,QAAQ;AAAA,IACrB,SAAS,QAAQ;AAAA,IACjB,WAAW,QAAQ;AAAA,IACnB,WAAW,QAAQ;AAAA,IACnB,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,UAAU,QAAQ;AAAA,IAClB,UAAU,QAAQ;AAAA,IAClB,gBAAgB,QAAQ;AAAA,EAC1B;AACF;AAOA,IAAM,gBAAgC,mDAAmC;AAAA,EACvE,KAAK;AAAA,EACL,SAAS,CAAC,yBAAsB,eAAa;AAAA,EAC7C,KAAK,OAAO;AACV,QAAI,OAAO,YAAY,aAAa;AAClC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,KAAK,MAAM,IAAI,MAAM,MAAM,GAAG;AAAA,QAC9B,SAAS,MAAM,IAAI;AAAA,UACjB;AAAA,YACE;AAAA,YACA,MAAM,OAAO,MAAM,MAAM,MAAM,EAAE,YAAY,IAAI;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,KAAK,IAAI,MAAM,MAAM,GAAG;AAAA,QACxB,SAAS,IAAI,MAAM,qBAAqB,OAAO,MAAM,MAAM,EAAE,IAAI,CAAC;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,iBACA,IAAI,UAAU,KAAK,GAAG,IACtB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,GAAG;AAAA,MACxB,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,kBAAQ;;;ACzEf,IAAAC,mBAA6B;AAI7B,SAAS,sBAAsB,SAAiC;AAC9D,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,IAChB,YAAY,QAAQ;AAAA,EACtB;AACF;AAOA,IAAM,iBAAiC,mDAAqC;AAAA,EAC1E,KAAK;AAAA,EACL,SAAS,CAAC,yBAAsB,eAAa;AAAA,EAC7C,KAAK,OAAO;AACV,QAAI,OAAO,aAAa,aAAa;AACnC,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO;AAAA,QACL,MAAM,MAAM,IAAI;AAAA,UACd,MAAM,OAAO,MAAM,MAAM,MAAM,EAAE,YAAY,IAAI;AAAA,QACnD;AAAA,QACA,SAAS,MAAM,IAAI,MAAM,sBAAsB,KAAK,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO;AAAA,QACL,MAAM,IAAI,MAAM,MAAM,MAAM,EAAE,IAAI;AAAA,QAClC,SAAS,IAAI,MAAM,sBAAsB,KAAK,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WACE,kBACA,IAAI,UAAU,KAAK,IAAI,IACvB,MACA,IAAI,UAAU,KAAK,OAAO,IAC1B;AAAA,EAEJ;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI;AAAA,MACT,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB,IAAI,YAAY,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACF,CAAC;AAED,IAAO,mBAAQ;;;AC3Df,IAAAC,mBAA6B;AAE7B,IAAM,YAA4B,mDAA+B;AAAA,EAC/D,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,QAAQ,aAAa;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO,IAAI,MAAM,MAAM,IAAI;AAAA,IAC7B;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,IACnC;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO,IAAI,MAAM,MAAM,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WAAO,aAAa,IAAI,UAAU,IAAI,IAAI;AAAA,EAC5C;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI,IAAI,IAAI,YAAY,IAAI,CAAW;AAAA,EAChD;AACF,CAAC;AAED,IAAO,cAAQ;;;AC7Bf,IAAAC,mBAA6B;AAE7B,IAAM,wBAAwC,mDAG5C;AAAA,EACA,KAAK;AAAA,EACL,KAAK,OAAO;AACV,QAAI,OAAO,oBAAoB,aAAa;AAC1C,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,IACL,KAAK,OAAO,KAAK;AACf,aAAO,IAAI,MAAM,MAAM,SAAS,CAAC;AAAA,IACnC;AAAA,IACA,MAAM,MAAM,OAAO,KAAK;AACtB,aAAO,MAAM,IAAI,MAAM,MAAM,SAAS,CAAC;AAAA,IACzC;AAAA,IACA,OAAO,OAAO,KAAK;AACjB,aAAO,IAAI,MAAM,MAAM,SAAS,CAAC;AAAA,IACnC;AAAA,EACF;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,WAAO,yBAAyB,IAAI,UAAU,IAAI,IAAI;AAAA,EACxD;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,WAAO,IAAI,gBAAgB,IAAI,YAAY,IAAI,CAAW;AAAA,EAC5D;AACF,CAAC;AAED,IAAO,4BAAQ;", "names": ["controller", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval", "import_seroval"]}