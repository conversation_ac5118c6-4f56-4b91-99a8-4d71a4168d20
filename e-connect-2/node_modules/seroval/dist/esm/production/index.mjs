var R=(a=>(a[a.AggregateError=1]="AggregateError",a[a.ArrowFunction=2]="ArrowFunction",a[a.ErrorPrototypeStack=4]="ErrorPrototypeStack",a[a.ObjectAssign=8]="ObjectAssign",a[a.BigIntTypedArray=16]="BigIntTypedArray",a))(R||{});function Nr(o){switch(o){case'"':return'\\"';case"\\":return"\\\\";case`
`:return"\\n";case"\r":return"\\r";case"\b":return"\\b";case"	":return"\\t";case"\f":return"\\f";case"<":return"\\x3C";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:return}}function d(o){let e="",r=0,t;for(let n=0,a=o.length;n<a;n++)t=Nr(o[n]),t&&(e+=o.slice(r,n)+t,r=n+1);return r===0?e=o:e+=o.slice(r),e}function br(o){switch(o){case"\\\\":return"\\";case'\\"':return'"';case"\\n":return`
`;case"\\r":return"\r";case"\\b":return"\b";case"\\t":return"	";case"\\f":return"\f";case"\\x3C":return"<";case"\\u2028":return"\u2028";case"\\u2029":return"\u2029";default:return o}}function N(o){return o.replace(/(\\\\|\\"|\\n|\\r|\\b|\\t|\\f|\\u2028|\\u2029|\\x3C)/g,br)}var O="__SEROVAL_REFS__",Q="$R",ae=`self.${Q}`;function xr(o){return o==null?`${ae}=${ae}||[]`:`(${ae}=${ae}||{})["${d(o)}"]=[]`}function f(o,e){if(!o)throw e}var Be=new Map,C=new Map;function Ir(o,e){return Be.set(e,o),C.set(o,e),e}function je(o){return Be.has(o)}function Ar(o){return C.has(o)}function Ke(o){return f(je(o),new ie(o)),Be.get(o)}function Je(o){return f(Ar(o),new le(o)),C.get(o)}typeof globalThis!="undefined"?Object.defineProperty(globalThis,O,{value:C,configurable:!0,writable:!1,enumerable:!1}):typeof window!="undefined"?Object.defineProperty(window,O,{value:C,configurable:!0,writable:!1,enumerable:!1}):typeof self!="undefined"?Object.defineProperty(self,O,{value:C,configurable:!0,writable:!1,enumerable:!1}):typeof global!="undefined"&&Object.defineProperty(global,O,{value:C,configurable:!0,writable:!1,enumerable:!1});function Hr(o){return o}function Ye(o,e){for(let r=0,t=e.length;r<t;r++){let n=e[r];o.has(n)||(o.add(n),n.extends&&Ye(o,n.extends))}}function m(o){if(o){let e=new Set;return Ye(e,o),[...e]}}var $e={0:"Symbol.asyncIterator",1:"Symbol.hasInstance",2:"Symbol.isConcatSpreadable",3:"Symbol.iterator",4:"Symbol.match",5:"Symbol.matchAll",6:"Symbol.replace",7:"Symbol.search",8:"Symbol.species",9:"Symbol.split",10:"Symbol.toPrimitive",11:"Symbol.toStringTag",12:"Symbol.unscopables"},ce={[Symbol.asyncIterator]:0,[Symbol.hasInstance]:1,[Symbol.isConcatSpreadable]:2,[Symbol.iterator]:3,[Symbol.match]:4,[Symbol.matchAll]:5,[Symbol.replace]:6,[Symbol.search]:7,[Symbol.species]:8,[Symbol.split]:9,[Symbol.toPrimitive]:10,[Symbol.toStringTag]:11,[Symbol.unscopables]:12},Ge={0:Symbol.asyncIterator,1:Symbol.hasInstance,2:Symbol.isConcatSpreadable,3:Symbol.iterator,4:Symbol.match,5:Symbol.matchAll,6:Symbol.replace,7:Symbol.search,8:Symbol.species,9:Symbol.split,10:Symbol.toPrimitive,11:Symbol.toStringTag,12:Symbol.unscopables},qe={2:"!0",3:"!1",1:"void 0",0:"null",4:"-0",5:"1/0",6:"-1/0",7:"0/0"},He={2:!0,3:!1,1:void 0,0:null,4:-0,5:Number.POSITIVE_INFINITY,6:Number.NEGATIVE_INFINITY,7:Number.NaN};var ue={0:"Error",1:"EvalError",2:"RangeError",3:"ReferenceError",4:"SyntaxError",5:"TypeError",6:"URIError"},Ze={0:Error,1:EvalError,2:RangeError,3:ReferenceError,4:SyntaxError,5:TypeError,6:URIError},s=void 0;function u(o,e,r,t,n,a,i,l,c,p,h,X){return{t:o,i:e,s:r,l:t,c:n,m:a,p:i,e:l,a:c,f:p,b:h,o:X}}function x(o){return u(2,s,o,s,s,s,s,s,s,s,s,s)}var I=x(2),A=x(3),pe=x(1),de=x(0),Xe=x(4),Qe=x(5),er=x(6),rr=x(7);function me(o){return o instanceof EvalError?1:o instanceof RangeError?2:o instanceof ReferenceError?3:o instanceof SyntaxError?4:o instanceof TypeError?5:o instanceof URIError?6:0}function wr(o){let e=ue[me(o)];return o.name!==e?{name:o.name}:o.constructor.name!==e?{name:o.constructor.name}:{}}function j(o,e){let r=wr(o),t=Object.getOwnPropertyNames(o);for(let n=0,a=t.length,i;n<a;n++)i=t[n],i!=="name"&&i!=="message"&&(i==="stack"?e&4&&(r=r||{},r[i]=o[i]):(r=r||{},r[i]=o[i]));return r}function fe(o){return Object.isFrozen(o)?3:Object.isSealed(o)?2:Object.isExtensible(o)?0:1}function ge(o){switch(o){case Number.POSITIVE_INFINITY:return Qe;case Number.NEGATIVE_INFINITY:return er}return o!==o?rr:Object.is(o,-0)?Xe:u(0,s,o,s,s,s,s,s,s,s,s,s)}function w(o){return u(1,s,d(o),s,s,s,s,s,s,s,s,s)}function Se(o){return u(3,s,""+o,s,s,s,s,s,s,s,s,s)}function sr(o){return u(4,o,s,s,s,s,s,s,s,s,s,s)}function he(o,e){let r=e.valueOf();return u(5,o,r!==r?"":e.toISOString(),s,s,s,s,s,s,s,s,s)}function ye(o,e){return u(6,o,s,s,d(e.source),e.flags,s,s,s,s,s,s)}function ve(o,e){let r=new Uint8Array(e),t=r.length,n=new Array(t);for(let a=0;a<t;a++)n[a]=r[a];return u(19,o,n,s,s,s,s,s,s,s,s,s)}function or(o,e){return u(17,o,ce[e],s,s,s,s,s,s,s,s,s)}function nr(o,e){return u(18,o,d(Ke(e)),s,s,s,s,s,s,s,s,s)}function _(o,e,r){return u(25,o,r,s,d(e),s,s,s,s,s,s,s)}function Ne(o,e,r){return u(9,o,s,e.length,s,s,s,s,r,s,s,fe(e))}function be(o,e){return u(21,o,s,s,s,s,s,s,s,e,s,s)}function xe(o,e,r){return u(15,o,s,e.length,e.constructor.name,s,s,s,s,r,e.byteOffset,s)}function Ie(o,e,r){return u(16,o,s,e.length,e.constructor.name,s,s,s,s,r,e.byteOffset,s)}function Ae(o,e,r){return u(20,o,s,e.byteLength,s,s,s,s,s,r,e.byteOffset,s)}function we(o,e,r){return u(13,o,me(e),s,s,d(e.message),r,s,s,s,s,s)}function Ee(o,e,r){return u(14,o,me(e),s,s,d(e.message),r,s,s,s,s,s)}function Pe(o,e,r){return u(7,o,s,e,s,s,s,s,r,s,s,s)}function M(o,e){return u(28,s,s,s,s,s,s,s,[o,e],s,s,s)}function U(o,e){return u(30,s,s,s,s,s,s,s,[o,e],s,s,s)}function L(o,e,r){return u(31,o,s,s,s,s,s,s,r,e,s,s)}function Re(o,e){return u(32,o,s,s,s,s,s,s,s,e,s,s)}function Oe(o,e){return u(33,o,s,s,s,s,s,s,s,e,s,s)}function Ce(o,e){return u(34,o,s,s,s,s,s,s,s,e,s,s)}var{toString:_e}=Object.prototype;function Er(o,e){return e instanceof Error?`Seroval caught an error during the ${o} process.
  
${e.name}
${e.message}

- For more information, please check the "cause" property of this error.
- If you believe this is an error in Seroval, please submit an issue at https://github.com/lxsmnsyc/seroval/issues/new`:`Seroval caught an error during the ${o} process.

"${_e.call(e)}"

For more information, please check the "cause" property of this error.`}var ee=class extends Error{constructor(r,t){super(Er(r,t));this.cause=t}},E=class extends ee{constructor(e){super("parsing",e)}},Te=class extends ee{constructor(e){super("serialization",e)}},ze=class extends ee{constructor(e){super("deserialization",e)}},g=class extends Error{constructor(r){super(`The value ${_e.call(r)} of type "${typeof r}" cannot be parsed/serialized.
      
There are few workarounds for this problem:
- Transform the value in a way that it can be serialized.
- If the reference is present on multiple runtimes (isomorphic), you can use the Reference API to map the references.`);this.value=r}},y=class extends Error{constructor(e){super('Unsupported node type "'+e.t+'".')}},W=class extends Error{constructor(e){super('Missing plugin for tag "'+e+'".')}},P=class extends Error{constructor(e){super('Missing "'+e+'" instance.')}},ie=class extends Error{constructor(r){super('Missing reference for the value "'+_e.call(r)+'" of type "'+typeof r+'"');this.value=r}},le=class extends Error{constructor(e){super('Missing reference for id "'+d(e)+'"')}},ke=class extends Error{constructor(e){super('Unknown TypedArray "'+e+'"')}};var T=class{constructor(e,r){this.value=e;this.replacement=r}};function z(o,e,r){return o&2?(e.length===1?e[0]:"("+e.join(",")+")")+"=>"+(r.startsWith("{")?"("+r+")":r):"function("+e.join(",")+"){return "+r+"}"}function S(o,e,r){return o&2?(e.length===1?e[0]:"("+e.join(",")+")")+"=>{"+r+"}":"function("+e.join(",")+"){"+r+"}"}var ar={},ir={};var lr={0:{},1:{},2:{},3:{},4:{}};function Pr(o){return z(o,["r"],"(r.p=new Promise("+S(o,["s","f"],"r.s=s,r.f=f")+"))")}function Rr(o){return S(o,["r","d"],"r.s(d),r.p.s=1,r.p.v=d")}function Or(o){return S(o,["r","d"],"r.f(d),r.p.s=2,r.p.v=d")}function Cr(o){return z(o,["b","a","s","l","p","f","e","n"],"(b=[],a=!0,s=!1,l=[],p=0,f="+S(o,["v","m","x"],"for(x=0;x<p;x++)l[x]&&l[x][m](v)")+",n="+S(o,["o","x","z","c"],'for(x=0,z=b.length;x<z;x++)(c=b[x],(!a&&x===z-1)?o[s?"return":"throw"](c):o.next(c))')+",e="+z(o,["o","t"],"(a&&(l[t=p++]=o),n(o),"+S(o,[],"a&&(l[t]=void 0)")+")")+",{__SEROVAL_STREAM__:!0,on:"+z(o,["o"],"e(o)")+",next:"+S(o,["v"],'a&&(b.push(v),f(v,"next"))')+",throw:"+S(o,["v"],'a&&(b.push(v),f(v,"throw"),a=s=!1,l.length=0)')+",return:"+S(o,["v"],'a&&(b.push(v),f(v,"return"),a=!1,s=!0,l.length=0)')+"})")}function cr(o,e){switch(e){case 0:return"[]";case 1:return Pr(o);case 2:return Rr(o);case 3:return Or(o);case 4:return Cr(o);default:return""}}function re(){let o,e;return{promise:new Promise((r,t)=>{o=r,e=t}),resolve(r){o(r)},reject(r){e(r)}}}function Fe(o){return"__SEROVAL_STREAM__"in o}function K(){let o=new Set,e=[],r=!0,t=!0;function n(l){for(let c of o.keys())c.next(l)}function a(l){for(let c of o.keys())c.throw(l)}function i(l){for(let c of o.keys())c.return(l)}return{__SEROVAL_STREAM__:!0,on(l){r&&o.add(l);for(let c=0,p=e.length;c<p;c++){let h=e[c];c===p-1&&!r?t?l.return(h):l.throw(h):l.next(h)}return()=>{r&&o.delete(l)}},next(l){r&&(e.push(l),n(l))},throw(l){r&&(e.push(l),a(l),r=!1,t=!1,o.clear())},return(l){r&&(e.push(l),i(l),r=!1,t=!0,o.clear())}}}function Ve(o){let e=K(),r=o[Symbol.asyncIterator]();async function t(){try{let n=await r.next();n.done?e.return(n.value):(e.next(n.value),await t())}catch(n){e.throw(n)}}return t().catch(()=>{}),e}function ur(o){return()=>{let e=[],r=[],t=0,n=-1,a=!1;function i(){for(let c=0,p=r.length;c<p;c++)r[c].resolve({done:!0,value:void 0})}o.on({next(c){let p=r.shift();p&&p.resolve({done:!1,value:c}),e.push(c)},throw(c){let p=r.shift();p&&p.reject(c),i(),n=e.length,e.push(c),a=!0},return(c){let p=r.shift();p&&p.resolve({done:!0,value:c}),i(),n=e.length,e.push(c)}});function l(){let c=t++,p=e[c];if(c!==n)return{done:!1,value:p};if(a)throw p;return{done:!0,value:p}}return{[Symbol.asyncIterator](){return this},async next(){if(n===-1){let c=t++;if(c>=e.length){let p=re();return r.push(p),await p.promise}return{done:!1,value:e[c]}}return t>n?{done:!0,value:void 0}:l()}}}}function J(o){let e=[],r=-1,t=-1,n=o[Symbol.iterator]();for(;;)try{let a=n.next();if(e.push(a.value),a.done){t=e.length-1;break}}catch(a){r=e.length,e.push(a)}return{v:e,t:r,d:t}}function pr(o){return()=>{let e=0;return{[Symbol.iterator](){return this},next(){if(e>o.d)return{done:!0,value:s};let r=e++,t=o.v[r];if(r===o.t)throw t;return{done:r===o.d,value:t}}}}}async function Me(o){try{return[1,await o]}catch(e){return[0,e]}}var Y=class{constructor(e){this.marked=new Set;this.plugins=e.plugins,this.features=31^(e.disabledFeatures||0),this.refs=e.refs||new Map}markRef(e){this.marked.add(e)}isMarked(e){return this.marked.has(e)}createIndex(e){let r=this.refs.size;return this.refs.set(e,r),r}getIndexedValue(e){let r=this.refs.get(e);return r!=null?(this.markRef(r),{type:1,value:sr(r)}):{type:0,value:this.createIndex(e)}}getReference(e){let r=this.getIndexedValue(e);return r.type===1?r:je(e)?{type:2,value:nr(r.value,e)}:r}parseWellKnownSymbol(e){let r=this.getReference(e);return r.type!==0?r.value:(f(e in ce,new g(e)),or(r.value,e))}parseSpecialReference(e){let r=this.getIndexedValue(lr[e]);return r.type===1?r.value:u(26,r.value,e,s,s,s,s,s,s,s,s,s)}parseIteratorFactory(){let e=this.getIndexedValue(ar);return e.type===1?e.value:u(27,e.value,s,s,s,s,s,s,s,this.parseWellKnownSymbol(Symbol.iterator),s,s)}parseAsyncIteratorFactory(){let e=this.getIndexedValue(ir);return e.type===1?e.value:u(29,e.value,s,s,s,s,s,s,[this.parseSpecialReference(1),this.parseWellKnownSymbol(Symbol.asyncIterator)],s,s,s)}createObjectNode(e,r,t,n){return u(t?11:10,e,s,s,s,s,n,s,s,s,s,fe(r))}createMapNode(e,r,t,n){return u(8,e,s,s,s,s,s,{k:r,v:t,s:n},s,this.parseSpecialReference(0),s,s)}createPromiseConstructorNode(e,r){return u(22,e,r,s,s,s,s,s,s,this.parseSpecialReference(1),s,s)}};var k=class extends Y{async parseItems(e){let r=[];for(let t=0,n=e.length;t<n;t++)t in e&&(r[t]=await this.parse(e[t]));return r}async parseArray(e,r){return Ne(e,r,await this.parseItems(r))}async parseProperties(e){let r=Object.entries(e),t=[],n=[];for(let i=0,l=r.length;i<l;i++)t.push(d(r[i][0])),n.push(await this.parse(r[i][1]));let a=Symbol.iterator;return a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(M(this.parseIteratorFactory(),await this.parse(J(e))))),a=Symbol.asyncIterator,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(U(this.parseAsyncIteratorFactory(),await this.parse(Ve(e))))),a=Symbol.toStringTag,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(w(e[a]))),a=Symbol.isConcatSpreadable,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(e[a]?I:A)),{k:t,v:n,s:t.length}}async parsePlainObject(e,r,t){return this.createObjectNode(e,r,t,await this.parseProperties(r))}async parseBoxed(e,r){return be(e,await this.parse(r.valueOf()))}async parseTypedArray(e,r){return xe(e,r,await this.parse(r.buffer))}async parseBigIntTypedArray(e,r){return Ie(e,r,await this.parse(r.buffer))}async parseDataView(e,r){return Ae(e,r,await this.parse(r.buffer))}async parseError(e,r){let t=j(r,this.features);return we(e,r,t?await this.parseProperties(t):s)}async parseAggregateError(e,r){let t=j(r,this.features);return Ee(e,r,t?await this.parseProperties(t):s)}async parseMap(e,r){let t=[],n=[];for(let[a,i]of r.entries())t.push(await this.parse(a)),n.push(await this.parse(i));return this.createMapNode(e,t,n,r.size)}async parseSet(e,r){let t=[];for(let n of r.keys())t.push(await this.parse(n));return Pe(e,r.size,t)}async parsePromise(e,r){let[t,n]=await Me(r);return u(12,e,t,s,s,s,s,s,s,await this.parse(n),s,s)}async parsePlugin(e,r){let t=this.plugins;if(t)for(let n=0,a=t.length;n<a;n++){let i=t[n];if(i.parse.async&&i.test(r))return _(e,i.tag,await i.parse.async(r,this,{id:e}))}return s}async parseStream(e,r){return L(e,this.parseSpecialReference(4),await new Promise((t,n)=>{let a=[],i=r.on({next:l=>{this.markRef(e),this.parse(l).then(c=>{a.push(Re(e,c))},c=>{n(c),i()})},throw:l=>{this.markRef(e),this.parse(l).then(c=>{a.push(Oe(e,c)),t(a),i()},c=>{n(c),i()})},return:l=>{this.markRef(e),this.parse(l).then(c=>{a.push(Ce(e,c)),t(a),i()},c=>{n(c),i()})}})}))}async parseObject(e,r){if(Array.isArray(r))return this.parseArray(e,r);if(Fe(r))return this.parseStream(e,r);let t=r.constructor;if(t===T)return this.parse(r.replacement);let n=await this.parsePlugin(e,r);if(n)return n;switch(t){case Object:return this.parsePlainObject(e,r,!1);case s:return this.parsePlainObject(e,r,!0);case Date:return he(e,r);case RegExp:return ye(e,r);case Error:case EvalError:case RangeError:case ReferenceError:case SyntaxError:case TypeError:case URIError:return this.parseError(e,r);case Number:case Boolean:case String:case BigInt:return this.parseBoxed(e,r);case ArrayBuffer:return ve(e,r);case Int8Array:case Int16Array:case Int32Array:case Uint8Array:case Uint16Array:case Uint32Array:case Uint8ClampedArray:case Float32Array:case Float64Array:return this.parseTypedArray(e,r);case DataView:return this.parseDataView(e,r);case Map:return this.parseMap(e,r);case Set:return this.parseSet(e,r);default:break}if(t===Promise||r instanceof Promise)return this.parsePromise(e,r);let a=this.features;if(a&16)switch(t){case BigInt64Array:case BigUint64Array:return this.parseBigIntTypedArray(e,r);default:break}if(a&1&&typeof AggregateError!="undefined"&&(t===AggregateError||r instanceof AggregateError))return this.parseAggregateError(e,r);if(r instanceof Error)return this.parseError(e,r);if(Symbol.iterator in r||Symbol.asyncIterator in r)return this.parsePlainObject(e,r,!!t);throw new g(r)}async parseFunction(e){let r=this.getReference(e);if(r.type!==0)return r.value;let t=await this.parsePlugin(r.value,e);if(t)return t;throw new g(e)}async parse(e){switch(typeof e){case"boolean":return e?I:A;case"undefined":return pe;case"string":return w(e);case"number":return ge(e);case"bigint":return Se(e);case"object":{if(e){let r=this.getReference(e);return r.type===0?await this.parseObject(r.value,e):r.value}return de}case"symbol":return this.parseWellKnownSymbol(e);case"function":return this.parseFunction(e);default:throw new g(e)}}async parseTop(e){try{return await this.parse(e)}catch(r){throw r instanceof E?r:new E(r)}}};var $=class extends k{constructor(){super(...arguments);this.mode="cross"}};function dr(o){switch(o){case"Int8Array":return Int8Array;case"Int16Array":return Int16Array;case"Int32Array":return Int32Array;case"Uint8Array":return Uint8Array;case"Uint16Array":return Uint16Array;case"Uint32Array":return Uint32Array;case"Uint8ClampedArray":return Uint8ClampedArray;case"Float32Array":return Float32Array;case"Float64Array":return Float64Array;case"BigInt64Array":return BigInt64Array;case"BigUint64Array":return BigUint64Array;default:throw new ke(o)}}function mr(o,e){switch(e){case 3:return Object.freeze(o);case 1:return Object.preventExtensions(o);case 2:return Object.seal(o);default:return o}}var F=class{constructor(e){this.plugins=e.plugins,this.refs=e.refs||new Map}deserializeReference(e){return this.assignIndexedValue(e.i,Je(N(e.s)))}deserializeArray(e){let r=e.l,t=this.assignIndexedValue(e.i,new Array(r)),n;for(let a=0;a<r;a++)n=e.a[a],n&&(t[a]=this.deserialize(n));return mr(t,e.o),t}deserializeProperties(e,r){let t=e.s;if(t){let n=e.k,a=e.v;for(let i=0,l;i<t;i++)l=n[i],typeof l=="string"?r[N(l)]=this.deserialize(a[i]):r[this.deserialize(l)]=this.deserialize(a[i])}return r}deserializeObject(e){let r=this.assignIndexedValue(e.i,e.t===10?{}:Object.create(null));return this.deserializeProperties(e.p,r),mr(r,e.o),r}deserializeDate(e){return this.assignIndexedValue(e.i,new Date(e.s))}deserializeRegExp(e){return this.assignIndexedValue(e.i,new RegExp(N(e.c),e.m))}deserializeSet(e){let r=this.assignIndexedValue(e.i,new Set),t=e.a;for(let n=0,a=e.l;n<a;n++)r.add(this.deserialize(t[n]));return r}deserializeMap(e){let r=this.assignIndexedValue(e.i,new Map),t=e.e.k,n=e.e.v;for(let a=0,i=e.e.s;a<i;a++)r.set(this.deserialize(t[a]),this.deserialize(n[a]));return r}deserializeArrayBuffer(e){let r=new Uint8Array(e.s);return this.assignIndexedValue(e.i,r.buffer)}deserializeTypedArray(e){let r=dr(e.c),t=this.deserialize(e.f);return this.assignIndexedValue(e.i,new r(t,e.b,e.l))}deserializeDataView(e){let r=this.deserialize(e.f);return this.assignIndexedValue(e.i,new DataView(r,e.b,e.l))}deserializeDictionary(e,r){if(e.p){let t=this.deserializeProperties(e.p,{});Object.assign(r,t)}return r}deserializeAggregateError(e){let r=this.assignIndexedValue(e.i,new AggregateError([],N(e.m)));return this.deserializeDictionary(e,r)}deserializeError(e){let r=Ze[e.s],t=this.assignIndexedValue(e.i,new r(N(e.m)));return this.deserializeDictionary(e,t)}deserializePromise(e){let r=re(),t=this.assignIndexedValue(e.i,r),n=this.deserialize(e.f);return e.s?r.resolve(n):r.reject(n),t.promise}deserializeBoxed(e){return this.assignIndexedValue(e.i,Object(this.deserialize(e.f)))}deserializePlugin(e){let r=this.plugins;if(r){let t=N(e.c);for(let n=0,a=r.length;n<a;n++){let i=r[n];if(i.tag===t)return this.assignIndexedValue(e.i,i.deserialize(e.s,this,{id:e.i}))}}throw new W(e.c)}deserializePromiseConstructor(e){return this.assignIndexedValue(e.i,this.assignIndexedValue(e.s,re()).promise)}deserializePromiseResolve(e){let r=this.refs.get(e.i);f(r,new P("Promise")),r.resolve(this.deserialize(e.a[1]))}deserializePromiseReject(e){let r=this.refs.get(e.i);f(r,new P("Promise")),r.reject(this.deserialize(e.a[1]))}deserializeIteratorFactoryInstance(e){this.deserialize(e.a[0]);let r=this.deserialize(e.a[1]);return pr(r)}deserializeAsyncIteratorFactoryInstance(e){this.deserialize(e.a[0]);let r=this.deserialize(e.a[1]);return ur(r)}deserializeStreamConstructor(e){let r=this.assignIndexedValue(e.i,K()),t=e.a.length;if(t)for(let n=0;n<t;n++)this.deserialize(e.a[n]);return r}deserializeStreamNext(e){let r=this.refs.get(e.i);f(r,new P("Stream")),r.next(this.deserialize(e.f))}deserializeStreamThrow(e){let r=this.refs.get(e.i);f(r,new P("Stream")),r.throw(this.deserialize(e.f))}deserializeStreamReturn(e){let r=this.refs.get(e.i);f(r,new P("Stream")),r.return(this.deserialize(e.f))}deserializeIteratorFactory(e){this.deserialize(e.f)}deserializeAsyncIteratorFactory(e){this.deserialize(e.a[1])}deserializeTop(e){try{return this.deserialize(e)}catch(r){throw new ze(r)}}deserialize(e){switch(e.t){case 2:return He[e.s];case 0:return e.s;case 1:return N(e.s);case 3:return BigInt(e.s);case 4:return this.refs.get(e.i);case 18:return this.deserializeReference(e);case 9:return this.deserializeArray(e);case 10:case 11:return this.deserializeObject(e);case 5:return this.deserializeDate(e);case 6:return this.deserializeRegExp(e);case 7:return this.deserializeSet(e);case 8:return this.deserializeMap(e);case 19:return this.deserializeArrayBuffer(e);case 16:case 15:return this.deserializeTypedArray(e);case 20:return this.deserializeDataView(e);case 14:return this.deserializeAggregateError(e);case 13:return this.deserializeError(e);case 12:return this.deserializePromise(e);case 17:return Ge[e.s];case 21:return this.deserializeBoxed(e);case 25:return this.deserializePlugin(e);case 22:return this.deserializePromiseConstructor(e);case 23:return this.deserializePromiseResolve(e);case 24:return this.deserializePromiseReject(e);case 28:return this.deserializeIteratorFactoryInstance(e);case 30:return this.deserializeAsyncIteratorFactoryInstance(e);case 31:return this.deserializeStreamConstructor(e);case 32:return this.deserializeStreamNext(e);case 33:return this.deserializeStreamThrow(e);case 34:return this.deserializeStreamReturn(e);case 27:return this.deserializeIteratorFactory(e);case 29:return this.deserializeAsyncIteratorFactory(e);default:throw new y(e)}}};var te=class extends F{constructor(){super(...arguments);this.mode="cross"}assignIndexedValue(r,t){return this.refs.has(r)||this.refs.set(r,t),t}};var kr=/^[$A-Z_][0-9A-Z_$]*$/i;function Le(o){let e=o[0];return(e==="$"||e==="_"||e>="A"&&e<="Z"||e>="a"&&e<="z")&&kr.test(o)}function se(o){switch(o.t){case 0:return o.s+"="+o.v;case 2:return o.s+".set("+o.k+","+o.v+")";case 1:return o.s+".add("+o.v+")";case 3:return o.s+".delete("+o.k+")"}}function Fr(o){let e=[],r=o[0];for(let t=1,n=o.length,a,i=r;t<n;t++)a=o[t],a.t===0&&a.v===i.v?r={t:0,s:a.s,k:s,v:se(r)}:a.t===2&&a.s===i.s?r={t:2,s:se(r),k:a.k,v:a.v}:a.t===1&&a.s===i.s?r={t:1,s:se(r),k:s,v:a.v}:a.t===3&&a.s===i.s?r={t:3,s:se(r),k:a.k,v:s}:(e.push(r),r=a),i=a;return e.push(r),e}function fr(o){if(o.length){let e="",r=Fr(o);for(let t=0,n=r.length;t<n;t++)e+=se(r[t])+",";return e}return s}var Vr="Object.create(null)",Dr="new Set",Br="new Map",jr="Promise.resolve",_r="Promise.reject",Mr={3:"Object.freeze",2:"Object.seal",1:"Object.preventExtensions",0:s},V=class{constructor(e){this.stack=[];this.flags=[];this.assignments=[];this.plugins=e.plugins,this.features=e.features,this.marked=new Set(e.markedRefs)}createFunction(e,r){return z(this.features,e,r)}createEffectfulFunction(e,r){return S(this.features,e,r)}markRef(e){this.marked.add(e)}isMarked(e){return this.marked.has(e)}pushObjectFlag(e,r){e!==0&&(this.markRef(r),this.flags.push({type:e,value:this.getRefParam(r)}))}resolveFlags(){let e="";for(let r=0,t=this.flags,n=t.length;r<n;r++){let a=t[r];e+=Mr[a.type]+"("+a.value+"),"}return e}resolvePatches(){let e=fr(this.assignments),r=this.resolveFlags();return e?r?e+r:e:r}createAssignment(e,r){this.assignments.push({t:0,s:e,k:s,v:r})}createAddAssignment(e,r){this.assignments.push({t:1,s:this.getRefParam(e),k:s,v:r})}createSetAssignment(e,r,t){this.assignments.push({t:2,s:this.getRefParam(e),k:r,v:t})}createDeleteAssignment(e,r){this.assignments.push({t:3,s:this.getRefParam(e),k:r,v:s})}createArrayAssign(e,r,t){this.createAssignment(this.getRefParam(e)+"["+r+"]",t)}createObjectAssign(e,r,t){this.createAssignment(this.getRefParam(e)+"."+r,t)}isIndexedValueInStack(e){return e.t===4&&this.stack.includes(e.i)}serializeReference(e){return this.assignIndexedValue(e.i,O+'.get("'+e.s+'")')}serializeArrayItem(e,r,t){return r?this.isIndexedValueInStack(r)?(this.markRef(e),this.createArrayAssign(e,t,this.getRefParam(r.i)),""):this.serialize(r):""}serializeArray(e){let r=e.i;if(e.l){this.stack.push(r);let t=e.a,n=this.serializeArrayItem(r,t[0],0),a=n==="";for(let i=1,l=e.l,c;i<l;i++)c=this.serializeArrayItem(r,t[i],i),n+=","+c,a=c==="";return this.stack.pop(),this.pushObjectFlag(e.o,e.i),this.assignIndexedValue(r,"["+n+(a?",]":"]"))}return this.assignIndexedValue(r,"[]")}serializeProperty(e,r,t){if(typeof r=="string"){let n=Number(r),a=n>=0&&n.toString()===r||Le(r);if(this.isIndexedValueInStack(t)){let i=this.getRefParam(t.i);return this.markRef(e.i),a&&n!==n?this.createObjectAssign(e.i,r,i):this.createArrayAssign(e.i,a?r:'"'+r+'"',i),""}return(a?r:'"'+r+'"')+":"+this.serialize(t)}return"["+this.serialize(r)+"]:"+this.serialize(t)}serializeProperties(e,r){let t=r.s;if(t){let n=r.k,a=r.v;this.stack.push(e.i);let i=this.serializeProperty(e,n[0],a[0]);for(let l=1,c=i;l<t;l++)c=this.serializeProperty(e,n[l],a[l]),i+=(c&&i&&",")+c;return this.stack.pop(),"{"+i+"}"}return"{}"}serializeObject(e){return this.pushObjectFlag(e.o,e.i),this.assignIndexedValue(e.i,this.serializeProperties(e,e.p))}serializeWithObjectAssign(e,r,t){let n=this.serializeProperties(e,r);return n!=="{}"?"Object.assign("+t+","+n+")":t}serializeStringKeyAssignment(e,r,t,n){let a=this.serialize(n),i=Number(t),l=i>=0&&i.toString()===t||Le(t);if(this.isIndexedValueInStack(n))l&&i!==i?this.createObjectAssign(e.i,t,a):this.createArrayAssign(e.i,l?t:'"'+t+'"',a);else{let c=this.assignments;this.assignments=r,l&&i!==i?this.createObjectAssign(e.i,t,a):this.createArrayAssign(e.i,l?t:'"'+t+'"',a),this.assignments=c}}serializeAssignment(e,r,t,n){if(typeof t=="string")this.serializeStringKeyAssignment(e,r,t,n);else{let a=this.stack;this.stack=[];let i=this.serialize(n);this.stack=a;let l=this.assignments;this.assignments=r,this.createArrayAssign(e.i,this.serialize(t),i),this.assignments=l}}serializeAssignments(e,r){let t=r.s;if(t){let n=[],a=r.k,i=r.v;this.stack.push(e.i);for(let l=0;l<t;l++)this.serializeAssignment(e,n,a[l],i[l]);return this.stack.pop(),fr(n)}return s}serializeDictionary(e,r){if(e.p)if(this.features&8)r=this.serializeWithObjectAssign(e,e.p,r);else{this.markRef(e.i);let t=this.serializeAssignments(e,e.p);if(t)return"("+this.assignIndexedValue(e.i,r)+","+t+this.getRefParam(e.i)+")"}return this.assignIndexedValue(e.i,r)}serializeNullConstructor(e){return this.pushObjectFlag(e.o,e.i),this.serializeDictionary(e,Vr)}serializeDate(e){return this.assignIndexedValue(e.i,'new Date("'+e.s+'")')}serializeRegExp(e){return this.assignIndexedValue(e.i,"/"+e.c+"/"+e.m)}serializeSetItem(e,r){return this.isIndexedValueInStack(r)?(this.markRef(e),this.createAddAssignment(e,this.getRefParam(r.i)),""):this.serialize(r)}serializeSet(e){let r=Dr,t=e.l,n=e.i;if(t){let a=e.a;this.stack.push(n);let i=this.serializeSetItem(n,a[0]);for(let l=1,c=i;l<t;l++)c=this.serializeSetItem(n,a[l]),i+=(c&&i&&",")+c;this.stack.pop(),i&&(r+="(["+i+"])")}return this.assignIndexedValue(n,r)}serializeMapEntry(e,r,t,n){if(this.isIndexedValueInStack(r)){let a=this.getRefParam(r.i);if(this.markRef(e),this.isIndexedValueInStack(t)){let l=this.getRefParam(t.i);return this.createSetAssignment(e,a,l),""}if(t.t!==4&&t.i!=null&&this.isMarked(t.i)){let l="("+this.serialize(t)+",["+n+","+n+"])";return this.createSetAssignment(e,a,this.getRefParam(t.i)),this.createDeleteAssignment(e,n),l}let i=this.stack;return this.stack=[],this.createSetAssignment(e,a,this.serialize(t)),this.stack=i,""}if(this.isIndexedValueInStack(t)){let a=this.getRefParam(t.i);if(this.markRef(e),r.t!==4&&r.i!=null&&this.isMarked(r.i)){let l="("+this.serialize(r)+",["+n+","+n+"])";return this.createSetAssignment(e,this.getRefParam(r.i),a),this.createDeleteAssignment(e,n),l}let i=this.stack;return this.stack=[],this.createSetAssignment(e,this.serialize(r),a),this.stack=i,""}return"["+this.serialize(r)+","+this.serialize(t)+"]"}serializeMap(e){let r=Br,t=e.e.s,n=e.i,a=e.f,i=this.getRefParam(a.i);if(t){let l=e.e.k,c=e.e.v;this.stack.push(n);let p=this.serializeMapEntry(n,l[0],c[0],i);for(let h=1,X=p;h<t;h++)X=this.serializeMapEntry(n,l[h],c[h],i),p+=(X&&p&&",")+X;this.stack.pop(),p&&(r+="(["+p+"])")}return a.t===26&&(this.markRef(a.i),r="("+this.serialize(a)+","+r+")"),this.assignIndexedValue(n,r)}serializeArrayBuffer(e){let r="new Uint8Array(",t=e.s,n=t.length;if(n){r+="["+t[0];for(let a=1;a<n;a++)r+=","+t[a];r+="]"}return this.assignIndexedValue(e.i,r+").buffer")}serializeTypedArray(e){return this.assignIndexedValue(e.i,"new "+e.c+"("+this.serialize(e.f)+","+e.b+","+e.l+")")}serializeDataView(e){return this.assignIndexedValue(e.i,"new DataView("+this.serialize(e.f)+","+e.b+","+e.l+")")}serializeAggregateError(e){let r=e.i;this.stack.push(r);let t=this.serializeDictionary(e,'new AggregateError([],"'+e.m+'")');return this.stack.pop(),t}serializeError(e){return this.serializeDictionary(e,"new "+ue[e.s]+'("'+e.m+'")')}serializePromise(e){let r,t=e.f,n=e.i,a=e.s?jr:_r;if(this.isIndexedValueInStack(t)){let i=this.getRefParam(t.i);r=a+(e.s?"().then("+this.createFunction([],i)+")":"().catch("+this.createEffectfulFunction([],"throw "+i)+")")}else{this.stack.push(n);let i=this.serialize(t);this.stack.pop(),r=a+"("+i+")"}return this.assignIndexedValue(n,r)}serializeWellKnownSymbol(e){return this.assignIndexedValue(e.i,$e[e.s])}serializeBoxed(e){return this.assignIndexedValue(e.i,"Object("+this.serialize(e.f)+")")}serializePlugin(e){let r=this.plugins;if(r)for(let t=0,n=r.length;t<n;t++){let a=r[t];if(a.tag===e.c)return this.assignIndexedValue(e.i,a.serialize(e.s,this,{id:e.i}))}throw new W(e.c)}getConstructor(e){let r=this.serialize(e);return r===this.getRefParam(e.i)?r:"("+r+")"}serializePromiseConstructor(e){let r=this.assignIndexedValue(e.s,"{p:0,s:0,f:0}");return this.assignIndexedValue(e.i,this.getConstructor(e.f)+"("+r+")")}serializePromiseResolve(e){return this.getConstructor(e.a[0])+"("+this.getRefParam(e.i)+","+this.serialize(e.a[1])+")"}serializePromiseReject(e){return this.getConstructor(e.a[0])+"("+this.getRefParam(e.i)+","+this.serialize(e.a[1])+")"}serializeSpecialReference(e){return this.assignIndexedValue(e.i,cr(this.features,e.s))}serializeIteratorFactory(e){let r="",t=!1;return e.f.t!==4&&(this.markRef(e.f.i),r="("+this.serialize(e.f)+",",t=!0),r+=this.assignIndexedValue(e.i,this.createFunction(["s"],this.createFunction(["i","c","d","t"],"(i=0,t={["+this.getRefParam(e.f.i)+"]:"+this.createFunction([],"t")+",next:"+this.createEffectfulFunction([],"if(i>s.d)return{done:!0,value:void 0};if(d=s.v[c=i++],c===s.t)throw d;return{done:c===s.d,value:d}")+"})"))),t&&(r+=")"),r}serializeIteratorFactoryInstance(e){return this.getConstructor(e.a[0])+"("+this.serialize(e.a[1])+")"}serializeAsyncIteratorFactory(e){let r=e.a[0],t=e.a[1],n="";r.t!==4&&(this.markRef(r.i),n+="("+this.serialize(r)),t.t!==4&&(this.markRef(t.i),n+=(n?",":"(")+this.serialize(t)),n&&(n+=",");let a=this.assignIndexedValue(e.i,this.createFunction(["s"],this.createFunction(["b","c","p","d","e","t","f"],"(b=[],c=0,p=[],d=-1,e=!1,f="+this.createEffectfulFunction(["i","l"],"for(i=0,l=p.length;i<l;i++)p[i].s({done:!0,value:void 0})")+",s.on({next:"+this.createEffectfulFunction(["v","t"],"if(t=p.shift())t.s({done:!1,value:v});b.push(v)")+",throw:"+this.createEffectfulFunction(["v","t"],"if(t=p.shift())t.f(v);f(),d=b.length,e=!0,b.push(v)")+",return:"+this.createEffectfulFunction(["v","t"],"if(t=p.shift())t.s({done:!0,value:v});f(),d=b.length,b.push(v)")+"}),t={["+this.getRefParam(t.i)+"]:"+this.createFunction([],"t.p")+",next:"+this.createEffectfulFunction(["i","t","v"],"if(d===-1){return((i=c++)>=b.length)?("+this.getRefParam(r.i)+"(t={p:0,s:0,f:0}),p.push(t),t.p):{done:!1,value:b[i]}}if(c>d)return{done:!0,value:void 0};if(v=b[i=c++],i!==d)return{done:!1,value:v};if(e)throw v;return{done:!0,value:v}")+"})")));return n?n+a+")":a}serializeAsyncIteratorFactoryInstance(e){return this.getConstructor(e.a[0])+"("+this.serialize(e.a[1])+")"}serializeStreamConstructor(e){let r=this.assignIndexedValue(e.i,this.getConstructor(e.f)+"()"),t=e.a.length;if(t){let n=this.serialize(e.a[0]);for(let a=1;a<t;a++)n+=","+this.serialize(e.a[a]);return"("+r+","+n+","+this.getRefParam(e.i)+")"}return r}serializeStreamNext(e){return this.getRefParam(e.i)+".next("+this.serialize(e.f)+")"}serializeStreamThrow(e){return this.getRefParam(e.i)+".throw("+this.serialize(e.f)+")"}serializeStreamReturn(e){return this.getRefParam(e.i)+".return("+this.serialize(e.f)+")"}serialize(e){try{switch(e.t){case 2:return qe[e.s];case 0:return""+e.s;case 1:return'"'+e.s+'"';case 3:return e.s+"n";case 4:return this.getRefParam(e.i);case 18:return this.serializeReference(e);case 9:return this.serializeArray(e);case 10:return this.serializeObject(e);case 11:return this.serializeNullConstructor(e);case 5:return this.serializeDate(e);case 6:return this.serializeRegExp(e);case 7:return this.serializeSet(e);case 8:return this.serializeMap(e);case 19:return this.serializeArrayBuffer(e);case 16:case 15:return this.serializeTypedArray(e);case 20:return this.serializeDataView(e);case 14:return this.serializeAggregateError(e);case 13:return this.serializeError(e);case 12:return this.serializePromise(e);case 17:return this.serializeWellKnownSymbol(e);case 21:return this.serializeBoxed(e);case 22:return this.serializePromiseConstructor(e);case 23:return this.serializePromiseResolve(e);case 24:return this.serializePromiseReject(e);case 25:return this.serializePlugin(e);case 26:return this.serializeSpecialReference(e);case 27:return this.serializeIteratorFactory(e);case 28:return this.serializeIteratorFactoryInstance(e);case 29:return this.serializeAsyncIteratorFactory(e);case 30:return this.serializeAsyncIteratorFactoryInstance(e);case 31:return this.serializeStreamConstructor(e);case 32:return this.serializeStreamNext(e);case 33:return this.serializeStreamThrow(e);case 34:return this.serializeStreamReturn(e);default:throw new y(e)}}catch(r){throw new Te(r)}}};var D=class extends V{constructor(r){super(r);this.mode="cross";this.scopeId=r.scopeId}getRefParam(r){return Q+"["+r+"]"}assignIndexedValue(r,t){return this.getRefParam(r)+"="+t}serializeTop(r){let t=this.serialize(r),n=r.i;if(n==null)return t;let a=this.resolvePatches(),i=this.getRefParam(n),l=this.scopeId==null?"":Q,c=a?"("+t+","+a+i+")":t;if(l==="")return r.t===10&&!a?"("+c+")":c;let p=this.scopeId==null?"()":"("+Q+'["'+d(this.scopeId)+'"])';return"("+this.createFunction([l],c)+")"+p}};var v=class extends Y{parseItems(e){let r=[];for(let t=0,n=e.length;t<n;t++)t in e&&(r[t]=this.parse(e[t]));return r}parseArray(e,r){return Ne(e,r,this.parseItems(r))}parseProperties(e){let r=Object.entries(e),t=[],n=[];for(let i=0,l=r.length;i<l;i++)t.push(d(r[i][0])),n.push(this.parse(r[i][1]));let a=Symbol.iterator;return a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(M(this.parseIteratorFactory(),this.parse(J(e))))),a=Symbol.asyncIterator,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(U(this.parseAsyncIteratorFactory(),this.parse(K())))),a=Symbol.toStringTag,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(w(e[a]))),a=Symbol.isConcatSpreadable,a in e&&(t.push(this.parseWellKnownSymbol(a)),n.push(e[a]?I:A)),{k:t,v:n,s:t.length}}parsePlainObject(e,r,t){return this.createObjectNode(e,r,t,this.parseProperties(r))}parseBoxed(e,r){return be(e,this.parse(r.valueOf()))}parseTypedArray(e,r){return xe(e,r,this.parse(r.buffer))}parseBigIntTypedArray(e,r){return Ie(e,r,this.parse(r.buffer))}parseDataView(e,r){return Ae(e,r,this.parse(r.buffer))}parseError(e,r){let t=j(r,this.features);return we(e,r,t?this.parseProperties(t):s)}parseAggregateError(e,r){let t=j(r,this.features);return Ee(e,r,t?this.parseProperties(t):s)}parseMap(e,r){let t=[],n=[];for(let[a,i]of r.entries())t.push(this.parse(a)),n.push(this.parse(i));return this.createMapNode(e,t,n,r.size)}parseSet(e,r){let t=[];for(let n of r.keys())t.push(this.parse(n));return Pe(e,r.size,t)}parsePlugin(e,r){let t=this.plugins;if(t)for(let n=0,a=t.length;n<a;n++){let i=t[n];if(i.parse.sync&&i.test(r))return _(e,i.tag,i.parse.sync(r,this,{id:e}))}}parseStream(e,r){return L(e,this.parseSpecialReference(4),[])}parsePromise(e,r){return this.createPromiseConstructorNode(e,this.createIndex({}))}parseObject(e,r){if(Array.isArray(r))return this.parseArray(e,r);if(Fe(r))return this.parseStream(e,r);let t=r.constructor;if(t===T)return this.parse(r.replacement);let n=this.parsePlugin(e,r);if(n)return n;switch(t){case Object:return this.parsePlainObject(e,r,!1);case void 0:return this.parsePlainObject(e,r,!0);case Date:return he(e,r);case RegExp:return ye(e,r);case Error:case EvalError:case RangeError:case ReferenceError:case SyntaxError:case TypeError:case URIError:return this.parseError(e,r);case Number:case Boolean:case String:case BigInt:return this.parseBoxed(e,r);case ArrayBuffer:return ve(e,r);case Int8Array:case Int16Array:case Int32Array:case Uint8Array:case Uint16Array:case Uint32Array:case Uint8ClampedArray:case Float32Array:case Float64Array:return this.parseTypedArray(e,r);case DataView:return this.parseDataView(e,r);case Map:return this.parseMap(e,r);case Set:return this.parseSet(e,r);default:break}if(t===Promise||r instanceof Promise)return this.parsePromise(e,r);let a=this.features;if(a&16)switch(t){case BigInt64Array:case BigUint64Array:return this.parseBigIntTypedArray(e,r);default:break}if(a&1&&typeof AggregateError!="undefined"&&(t===AggregateError||r instanceof AggregateError))return this.parseAggregateError(e,r);if(r instanceof Error)return this.parseError(e,r);if(Symbol.iterator in r||Symbol.asyncIterator in r)return this.parsePlainObject(e,r,!!t);throw new g(r)}parseFunction(e){let r=this.getReference(e);if(r.type!==0)return r.value;let t=this.parsePlugin(r.value,e);if(t)return t;throw new g(e)}parse(e){switch(typeof e){case"boolean":return e?I:A;case"undefined":return pe;case"string":return w(e);case"number":return ge(e);case"bigint":return Se(e);case"object":{if(e){let r=this.getReference(e);return r.type===0?this.parseObject(r.value,e):r.value}return de}case"symbol":return this.parseWellKnownSymbol(e);case"function":return this.parseFunction(e);default:throw new g(e)}}parseTop(e){try{return this.parse(e)}catch(r){throw r instanceof E?r:new E(r)}}};var oe=class extends v{constructor(r){super(r);this.alive=!0;this.pending=0;this.initial=!0;this.buffer=[];this.onParseCallback=r.onParse,this.onErrorCallback=r.onError,this.onDoneCallback=r.onDone}onParseInternal(r,t){try{this.onParseCallback(r,t)}catch(n){this.onError(n)}}flush(){for(let r=0,t=this.buffer.length;r<t;r++)this.onParseInternal(this.buffer[r],!1)}onParse(r){this.initial?this.buffer.push(r):this.onParseInternal(r,!1)}onError(r){if(this.onErrorCallback)this.onErrorCallback(r);else throw r}onDone(){this.onDoneCallback&&this.onDoneCallback()}pushPendingState(){this.pending++}popPendingState(){--this.pending<=0&&this.onDone()}parseProperties(r){let t=Object.entries(r),n=[],a=[];for(let l=0,c=t.length;l<c;l++)n.push(d(t[l][0])),a.push(this.parse(t[l][1]));let i=Symbol.iterator;return i in r&&(n.push(this.parseWellKnownSymbol(i)),a.push(M(this.parseIteratorFactory(),this.parse(J(r))))),i=Symbol.asyncIterator,i in r&&(n.push(this.parseWellKnownSymbol(i)),a.push(U(this.parseAsyncIteratorFactory(),this.parse(Ve(r))))),i=Symbol.toStringTag,i in r&&(n.push(this.parseWellKnownSymbol(i)),a.push(w(r[i]))),i=Symbol.isConcatSpreadable,i in r&&(n.push(this.parseWellKnownSymbol(i)),a.push(r[i]?I:A)),{k:n,v:a,s:n.length}}handlePromiseSuccess(r,t){let n=this.parseWithError(t);n&&this.onParse(u(23,r,s,s,s,s,s,s,[this.parseSpecialReference(2),n],s,s,s)),this.popPendingState()}handlePromiseFailure(r,t){if(this.alive){let n=this.parseWithError(t);n&&this.onParse(u(24,r,s,s,s,s,s,s,[this.parseSpecialReference(3),n],s,s,s))}this.popPendingState()}parsePromise(r,t){let n=this.createIndex({});return t.then(this.handlePromiseSuccess.bind(this,n),this.handlePromiseFailure.bind(this,n)),this.pushPendingState(),this.createPromiseConstructorNode(r,n)}parsePlugin(r,t){let n=this.plugins;if(n)for(let a=0,i=n.length;a<i;a++){let l=n[a];if(l.parse.stream&&l.test(t))return _(r,l.tag,l.parse.stream(t,this,{id:r}))}return s}parseStream(r,t){let n=L(r,this.parseSpecialReference(4),[]);return this.pushPendingState(),t.on({next:a=>{if(this.alive){let i=this.parseWithError(a);i&&this.onParse(Re(r,i))}},throw:a=>{if(this.alive){let i=this.parseWithError(a);i&&this.onParse(Oe(r,i))}this.popPendingState()},return:a=>{if(this.alive){let i=this.parseWithError(a);i&&this.onParse(Ce(r,i))}this.popPendingState()}}),n}parseWithError(r){try{return this.parse(r)}catch(t){return this.onError(t),s}}start(r){let t=this.parseWithError(r);t&&(this.onParseInternal(t,!0),this.initial=!1,this.flush(),this.pending<=0&&this.destroy())}destroy(){this.alive&&(this.onDone(),this.alive=!1)}isAlive(){return this.alive}};var G=class extends oe{constructor(){super(...arguments);this.mode="cross"}};var q=class extends v{constructor(){super(...arguments);this.mode="cross"}};function po(o,e={}){let r=m(e.plugins),t=new q({plugins:r,disabledFeatures:e.disabledFeatures,refs:e.refs}),n=t.parseTop(o);return new D({plugins:r,features:t.features,scopeId:e.scopeId,markedRefs:t.marked}).serializeTop(n)}async function mo(o,e={}){let r=m(e.plugins),t=new $({plugins:r,disabledFeatures:e.disabledFeatures,refs:e.refs}),n=await t.parseTop(o);return new D({plugins:r,features:t.features,scopeId:e.scopeId,markedRefs:t.marked}).serializeTop(n)}function fo(o,e={}){let r=m(e.plugins);return new q({plugins:r,disabledFeatures:e.disabledFeatures,refs:e.refs}).parseTop(o)}async function go(o,e={}){let r=m(e.plugins);return await new $({plugins:r,disabledFeatures:e.disabledFeatures,refs:e.refs}).parseTop(o)}function gr(o,e){let r=m(e.plugins),t=new G({plugins:r,refs:e.refs,disabledFeatures:e.disabledFeatures,onParse(n,a){let i=new D({plugins:r,features:t.features,scopeId:e.scopeId,markedRefs:t.marked}),l;try{l=i.serializeTop(n)}catch(c){e.onError&&e.onError(c);return}e.onSerialize(l,a)},onError:e.onError,onDone:e.onDone});return t.start(o),t.destroy.bind(t)}function So(o,e){let r=m(e.plugins),t=new G({plugins:r,refs:e.refs,disabledFeatures:e.disabledFeatures,onParse:e.onParse,onError:e.onError,onDone:e.onDone});return t.start(o),t.destroy.bind(t)}function ho(o,e){let r=m(e.plugins);return new te({plugins:r,refs:e.refs}).deserializeTop(o)}var H=class extends k{constructor(){super(...arguments);this.mode="vanilla"}};var ne=class extends F{constructor(r){super(r);this.mode="vanilla";this.marked=new Set(r.markedRefs)}assignIndexedValue(r,t){return this.marked.has(r)&&this.refs.set(r,t),t}};var yr="hjkmoquxzABCDEFGHIJKLNPQRTUVWXYZ$_",Sr=yr.length,vr="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$_",hr=vr.length;function We(o){let e=o%Sr,r=yr[e];for(o=(o-e)/Sr;o>0;)e=o%hr,r+=vr[e],o=(o-e)/hr;return r}var B=class extends V{constructor(){super(...arguments);this.mode="vanilla";this.valid=new Map;this.vars=[]}getRefParam(r){let t=this.valid.get(r);t==null&&(t=this.valid.size,this.valid.set(r,t));let n=this.vars[t];return n==null&&(n=We(t),this.vars[t]=n),n}assignIndexedValue(r,t){return this.isMarked(r)?this.getRefParam(r)+"="+t:t}serializePromiseConstructor(r){throw new y(r)}serializePromiseResolve(r){throw new y(r)}serializePromiseReject(r){throw new y(r)}serializeTop(r){let t=this.serialize(r);if(r.i!=null&&this.vars.length){let n=this.resolvePatches(),a=t;if(n){let i=this.getRefParam(r.i);a=t+","+n+i,t.startsWith(i+"=")||(a=i+"="+a),a="("+a+")"}return"("+this.createFunction(this.vars,a)+")()"}return r.t===10?"("+t+")":t}};var Z=class extends v{constructor(){super(...arguments);this.mode="vanilla"}};function Do(o,e={}){let r=m(e.plugins),t=new Z({plugins:r,disabledFeatures:e.disabledFeatures}),n=t.parseTop(o);return new B({plugins:r,features:t.features,markedRefs:t.marked}).serializeTop(n)}async function Bo(o,e={}){let r=m(e.plugins),t=new H({plugins:r,disabledFeatures:e.disabledFeatures}),n=await t.parseTop(o);return new B({plugins:r,features:t.features,markedRefs:t.marked}).serializeTop(n)}function jo(o){return(0,eval)(o)}function _o(o,e={}){let r=m(e.plugins),t=new Z({plugins:r,disabledFeatures:e.disabledFeatures});return{t:t.parseTop(o),f:t.features,m:Array.from(t.marked)}}async function Mo(o,e={}){let r=m(e.plugins),t=new H({plugins:r,disabledFeatures:e.disabledFeatures});return{t:await t.parseTop(o),f:t.features,m:Array.from(t.marked)}}function Uo(o,e={}){let r=m(e.plugins);return new B({plugins:r,features:o.f,markedRefs:o.m}).serializeTop(o.t)}function Lo(o,e={}){let r=m(e.plugins);return new ne({plugins:r,markedRefs:o.m}).deserializeTop(o.t)}var De=class{constructor(e){this.options=e;this.alive=!0;this.flushed=!1;this.done=!1;this.pending=0;this.cleanups=[];this.refs=new Map;this.keys=new Set;this.ids=0;this.plugins=m(e.plugins)}write(e,r){this.alive&&!this.flushed&&(this.pending++,this.keys.add(e),this.cleanups.push(gr(r,{plugins:this.plugins,scopeId:this.options.scopeId,refs:this.refs,disabledFeatures:this.options.disabledFeatures,onError:this.options.onError,onSerialize:(t,n)=>{this.alive&&this.options.onData(n?this.options.globalIdentifier+'["'+d(e)+'"]='+t:t)},onDone:()=>{this.alive&&(this.pending--,this.pending<=0&&this.flushed&&!this.done&&this.options.onDone&&(this.options.onDone(),this.done=!0))}})))}getNextID(){for(;this.keys.has(""+this.ids);)this.ids++;return""+this.ids}push(e){let r=this.getNextID();return this.write(r,e),r}flush(){this.alive&&(this.flushed=!0,this.pending<=0&&!this.done&&this.options.onDone&&(this.options.onDone(),this.done=!0))}close(){if(this.alive){for(let e=0,r=this.cleanups.length;e<r;e++)this.cleanups[e]();!this.done&&this.options.onDone&&(this.options.onDone(),this.done=!0),this.alive=!1}}};export{R as Feature,T as OpaqueReference,De as Serializer,ze as SerovalDeserializationError,ee as SerovalError,P as SerovalMissingInstanceError,W as SerovalMissingPluginError,ie as SerovalMissingReferenceError,le as SerovalMissingReferenceForIdError,E as SerovalParserError,Te as SerovalSerializationError,ke as SerovalUnknownTypedArrayError,y as SerovalUnsupportedNodeError,g as SerovalUnsupportedTypeError,Uo as compileJSON,Hr as createPlugin,Ir as createReference,K as createStream,po as crossSerialize,mo as crossSerializeAsync,gr as crossSerializeStream,jo as deserialize,ho as fromCrossJSON,Lo as fromJSON,xr as getCrossReferenceHeader,m as resolvePlugins,Do as serialize,Bo as serializeAsync,fo as toCrossJSON,go as toCrossJSONAsync,So as toCrossJSONStream,_o as toJSON,Mo as toJSONAsync};
