export declare const ITERATOR: {};
export declare const ASYNC_ITERATOR: {};
export declare const enum SpecialReference {
    MapSentinel = 0,
    PromiseConstructor = 1,
    PromiseSuccess = 2,
    PromiseFailure = 3,
    StreamConstructor = 4
}
/**
 * Placeholder references
 */
export declare const SPECIAL_REFS: Record<SpecialReference, unknown>;
export declare function serializeSpecialReferenceValue(features: number, ref: SpecialReference): string;
//# sourceMappingURL=special-reference.d.ts.map