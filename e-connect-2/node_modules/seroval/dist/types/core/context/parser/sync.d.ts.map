{"version": 3, "file": "sync.d.ts", "sourceRoot": "", "sources": ["../../../../../src/core/context/parser/sync.ts"], "names": [], "mappings": "AA+BA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAG3C,OAAO,KAAK,EACV,yBAAyB,EACzB,gBAAgB,EAChB,2BAA2B,EAC3B,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,0BAA0B,EAC1B,iBAAiB,EAEjB,uBAAuB,EACvB,iBAAiB,EACjB,6BAA6B,EAC7B,cAAc,EACd,qBAAqB,EACtB,MAAM,aAAa,CAAC;AAGrB,OAAO,KAAK,EACV,qBAAqB,EACrB,eAAe,EAChB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,WAAW,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAkB,MAAM,WAAW,CAAC;AAE9D,KAAK,cAAc,GAAG,iBAAiB,GAAG,0BAA0B,CAAC;AAErE,MAAM,MAAM,4BAA4B,GAAG,wBAAwB,CAAC;AAEpE,MAAM,CAAC,OAAO,CAAC,QAAQ,OAAO,qBAAsB,SAAQ,iBAAiB;IAC3E,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,WAAW,EAAE;IAUvD,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,gBAAgB;IAItE,SAAS,CAAC,eAAe,CACvB,UAAU,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,CAAC,GAC3C,uBAAuB;IAgD1B,SAAS,CAAC,gBAAgB,CACxB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAChC,KAAK,EAAE,OAAO,GACb,cAAc;IASjB,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,gBAAgB;IAInE,SAAS,CAAC,eAAe,CACvB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,eAAe,GACvB,qBAAqB;IAIxB,SAAS,CAAC,qBAAqB,CAC7B,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,qBAAqB,GAC7B,2BAA2B;IAI9B,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,GAAG,mBAAmB;IAI3E,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,GAAG,gBAAgB;IASlE,SAAS,CAAC,mBAAmB,CAC3B,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,cAAc,GACtB,yBAAyB;IAS5B,SAAS,CAAC,QAAQ,CAChB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,GAC7B,cAAc;IAUjB,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,cAAc;IAQrE,SAAS,CAAC,WAAW,CACnB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,OAAO,GACf,iBAAiB,GAAG,SAAS;IAmBhC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,WAAW;IAQzE,SAAS,CAAC,YAAY,CACpB,EAAE,EAAE,MAAM,EACV,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,GACzB,6BAA6B;IAKhC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,WAAW;IA0G/D,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,GAAG,WAAW;IAYtD,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,WAAW;IA+BjC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,WAAW;CASrC"}