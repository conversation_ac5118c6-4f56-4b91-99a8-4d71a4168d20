{"name": "@tanstack/router-devtools", "version": "1.125.6", "description": "Modern and scalable routing for React applications", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/router.git", "directory": "packages/router-devtools"}, "homepage": "https://tanstack.com/router", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "location", "router", "routing", "async", "async router", "typescript"], "type": "module", "types": "./dist/esm/index.d.ts", "main": "./dist/cjs/index.cjs", "module": "./dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "engines": {"node": ">=12"}, "dependencies": {"clsx": "^2.1.1", "goober": "^2.1.16", "@tanstack/react-router-devtools": "^1.125.6"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "peerDependencies": {"csstype": "^3.0.10", "react": ">=18.0.0 || >=19.0.0", "react-dom": ">=18.0.0 || >=19.0.0", "@tanstack/react-router": "^1.125.6"}, "peerDependenciesMeta": {"csstype": {"optional": true}}, "scripts": {}}