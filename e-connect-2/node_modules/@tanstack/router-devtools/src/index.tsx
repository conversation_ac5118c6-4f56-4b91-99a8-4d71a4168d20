console.warn(
  '[@tanstack/router-devtools] This package has moved to @tanstack/react-router-devtools. Please switch to the new package at your earliest convenience, as this package will be dropped in the next major version release.',
)

export { TanStackRouterDevtoolsInProd as TanStackRouterDevtools } from '@tanstack/react-router-devtools'
export { TanStackRouterDevtoolsPanelInProd as TanStackRouterDevtoolsPanel } from '@tanstack/react-router-devtools'
