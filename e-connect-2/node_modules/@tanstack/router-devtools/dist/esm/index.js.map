{"version": 3, "file": "index.js", "sources": ["../../src/index.tsx"], "sourcesContent": ["console.warn(\n  '[@tanstack/router-devtools] This package has moved to @tanstack/react-router-devtools. Please switch to the new package at your earliest convenience, as this package will be dropped in the next major version release.',\n)\n\nexport { TanStackRouterDevtoolsInProd as TanStackRouterDevtools } from '@tanstack/react-router-devtools'\nexport { TanStackRouterDevtoolsPanelInProd as TanStackRouterDevtoolsPanel } from '@tanstack/react-router-devtools'\n"], "names": [], "mappings": ";AAAA,QAAQ;AAAA,EACN;AACF;"}